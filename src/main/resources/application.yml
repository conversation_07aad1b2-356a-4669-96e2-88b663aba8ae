# 应用配置
server:
  port: 8080

spring:
  application:
    name: gis-data-import
  profiles:
    active: dev
  # 临时允许循环引用（后续需要重构移除）
  main:
    allow-circular-references: true
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  # 数据源配置
  datasource:
    druid:
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # Druid监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.zjxy.gisdataimport.shap
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    # 生产环境建议关闭SQL日志
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 模板功能配置
gis:
  template:
    enabled: true
    default-database: rights
    validation:
      enabled: true
    remote-database:
      enabled: true
    auto-create-table: false
  # 坐标转换配置
  coordinate:
    transform:
      # 是否启用坐标转换
      enabled: false
      # 源坐标系（Shapefile数据的原始坐标系）
      source-coord-system: ""
      # 目标坐标系（转换后存储的坐标系）
      target-coord-system: ""
      # 是否记录转换日志
      log-transformation: true
