/**
 * GIS导入任务数据验证前端脚本
 * 提供数据检查、进度监控、错误报告下载等功能
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */

class TaskValidationManager {
    constructor() {
        this.baseUrl = '/api/file-import';
        this.progressCheckInterval = null;
        this.progressCheckDelay = 2000; // 2秒检查一次进度
        this.currentTaskId = null; // 当前验证的任务ID
    }

    /**
     * 启动数据验证
     */
    async startValidation(taskId) {
        try {
            console.log('启动数据验证 - 任务ID:', taskId);

            // 设置当前任务ID
            this.currentTaskId = taskId;

            // 显示验证进度界面
            this.showValidationProgress(taskId);

            // 调用统一的验证API（同步验证，直接返回结果）
            const response = await fetch(`${this.baseUrl}/tasks/${taskId}/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            // 适配framework的Result格式
            if (result.code === "200" || result.code === 200) {
                console.log('验证完成成功:', result.msg || result.message);
                console.log('验证结果:', result.data);

                // 直接显示验证结果（不需要进度监控，因为验证是同步的）
                this.showValidationResult(result);

                // 刷新任务列表
                if (typeof refreshTaskList === 'function') {
                    refreshTaskList();
                }
            } else {
                console.error('验证失败:', result.msg || result.message);
                this.showError('验证失败: ' + (result.msg || result.message || '未知错误'));
                this.hideValidationProgress();
            }

        } catch (error) {
            console.error('验证异常:', error);
            this.showError('验证异常: ' + error.message);
            this.hideValidationProgress();
        }
    }

    /**
     * 开始监控验证进度
     */
    startProgressMonitoring(taskId) {
        this.progressCheckInterval = setInterval(async () => {
            try {
                const progress = await this.getValidationProgress(taskId);
                this.updateProgressDisplay(progress);

                // 检查是否完成
                if (progress.status === 'COMPLETED' || progress.progress >= 100) {
                    this.stopProgressMonitoring();
                    await this.handleValidationComplete(taskId);
                }

            } catch (error) {
                console.error('获取验证进度失败:', error);
                this.stopProgressMonitoring();
                this.showError('获取验证进度失败: ' + error.message);
            }
        }, this.progressCheckDelay);
    }

    /**
     * 停止进度监控
     */
    stopProgressMonitoring() {
        if (this.progressCheckInterval) {
            clearInterval(this.progressCheckInterval);
            this.progressCheckInterval = null;
        }
    }

    /**
     * 获取验证进度
     */
    async getValidationProgress(taskId) {
        try {
            const response = await fetch(`${this.baseUrl}/tasks/${taskId}/validation-progress`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            // 适配framework的Result格式
            if (result.code === "200" || result.code === 200) {
                return result;
            } else {
                // 如果没有进度信息，返回默认状态
                const errorMsg = result.msg || result.message;
                if (errorMsg && errorMsg.includes('未找到验证进度信息')) {
                    return {
                        code: "200",
                        msg: '验证尚未开始',
                        data: {
                            status: 'NOT_STARTED',
                            progress: 0
                        }
                    };
                }
                throw new Error(errorMsg || '获取验证进度失败');
            }
        } catch (error) {
            console.error('获取验证进度失败:', error);
            // 返回默认状态而不是抛出错误
            return {
                success: true,
                status: 'ERROR',
                message: '获取进度失败: ' + error.message,
                progress: 0
            };
        }
    }

    /**
     * 获取验证结果
     */
    async getValidationResult(taskId) {
        const response = await fetch(`${this.baseUrl}/tasks/${taskId}/validation-result`);
        const result = await response.json();

        // 适配framework的Result格式
        if (result.code === "200" || result.code === 200) {
            return result;
        } else {
            throw new Error(result.msg || result.message || '获取验证结果失败');
        }
    }

    /**
     * 处理验证完成
     */
    async handleValidationComplete(taskId) {
        try {
            const result = await this.getValidationResult(taskId);
            this.showValidationResult(result);

            // 刷新任务列表
            if (typeof refreshTaskList === 'function') {
                refreshTaskList();
            }

        } catch (error) {
            console.error('获取验证结果失败:', error);
            this.showError('获取验证结果失败: ' + error.message);
        }
    }

    /**
     * 显示验证进度界面
     */
    showValidationProgress(taskId) {
        const progressHtml = `
            <div id="validation-progress-modal" class="modal fade show" style="display: block;">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">数据验证进度</h5>
                            <button type="button" class="btn-close" onclick="taskValidation.cancelValidation(${taskId})"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">验证进度</label>
                                <div class="progress">
                                    <div id="validation-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%">0%</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">总记录数</h6>
                                            <h4 id="total-records" class="text-primary">-</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">已处理</h6>
                                            <h4 id="processed-records" class="text-info">-</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">错误记录</h6>
                                            <h4 id="error-records" class="text-warning">-</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">状态</h6>
                                            <h6 id="validation-status" class="text-success">验证中...</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <small id="validation-message" class="text-muted">正在启动数据验证...</small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="taskValidation.cancelValidation(${taskId})">
                                取消验证
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('validation-progress-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新的模态框
        document.body.insertAdjacentHTML('beforeend', progressHtml);
    }

    /**
     * 更新进度显示
     */
    updateProgressDisplay(progress) {
        const progressBar = document.getElementById('validation-progress-bar');
        const totalRecords = document.getElementById('total-records');
        const processedRecords = document.getElementById('processed-records');
        const errorRecords = document.getElementById('error-records');
        const validationStatus = document.getElementById('validation-status');
        const validationMessage = document.getElementById('validation-message');

        if (progressBar) {
            const percentage = Math.round(progress.progress || 0);
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '%';
        }

        if (totalRecords) totalRecords.textContent = progress.totalRecords || '-';
        if (processedRecords) processedRecords.textContent = progress.processedRecords || '-';
        if (errorRecords) errorRecords.textContent = progress.errorRecords || '-';

        if (validationStatus) {
            const statusText = progress.status === 'VALIDATING' ? '验证中...' :
                              progress.status === 'COMPLETED' ? '验证完成' : '未知状态';
            validationStatus.textContent = statusText;
        }

        if (validationMessage) {
            const message = progress.lastUpdateTime ?
                `最后更新: ${new Date(progress.lastUpdateTime).toLocaleTimeString()}` :
                '正在验证数据...';
            validationMessage.textContent = message;
        }
    }

    /**
     * 显示验证结果
     */
    showValidationResult(result) {
        this.hideValidationProgress();

        // 适配framework的Result格式，提取验证结果数据
        const validationData = result.data || result;
        const isPassed = validationData.passed || false;
        const totalRecords = validationData.totalRecords || 0;
        const validRecords = validationData.validRecords || 0;
        const errorRecords = validationData.errorRecords || 0;
        const errorRate = validationData.errorRate || 0;

        const resultHtml = `
            <div id="validation-result-modal" class="modal fade show" style="display: block;">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">数据验证结果</h5>
                            <button type="button" class="btn-close" onclick="taskValidation.hideValidationResult()"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert ${isPassed ? 'alert-success' : 'alert-danger'}" role="alert">
                                <h6 class="alert-heading">${isPassed ? '✅ 验证通过' : '❌ 验证失败'}</h6>
                                <p class="mb-0">数据验证已完成，共处理 ${totalRecords} 条记录</p>
                            </div>

                            <!-- 第一行：基础统计 -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">总记录数</h6>
                                            <h4 class="text-primary">${totalRecords}</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">有效记录</h6>
                                            <h4 class="text-success">${validRecords}</h4>
                                            <small class="text-muted">无错误记录</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">错误记录</h6>
                                            <h4 class="text-danger">${errorRecords}</h4>
                                            <small class="text-muted">包含错误的记录</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">错误率</h6>
                                            <h4 class="text-warning">${errorRate.toFixed(2)}%</h4>
                                            <small class="text-muted">错误比例</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第二行：验证详情 -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">验证状态</h6>
                                            <h4 class="${isPassed ? 'text-success' : 'text-danger'}">${isPassed ? '通过' : '失败'}</h4>
                                            <small class="text-muted">数据质量检查结果</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h6 class="card-title">成功率</h6>
                                            <h4 class="text-info">${totalRecords > 0 ? ((validRecords / totalRecords) * 100).toFixed(2) : 0}%</h4>
                                            <small class="text-muted">有效记录比例</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${errorRecords > 0 ? `
                                <div class="alert alert-warning">
                                    <h6>⚠️ 发现数据问题</h6>
                                    <p>共发现 ${errorRecords} 个错误，建议检查数据质量后重新验证。</p>
                                </div>
                            ` : `
                                <div class="alert alert-success">
                                    <h6>✅ 数据质量良好</h6>
                                    <p>所有数据都通过了验证，可以进行下一步操作。</p>
                                </div>
                            `}
                        </div>
                        <div class="modal-footer">
                            ${errorRecords > 0 ?
                                `<button type="button" class="btn btn-warning" onclick="taskValidation.downloadErrorReport(${this.currentTaskId})">
                                    <i class="fas fa-download"></i> 下载错误报告
                                </button>` : ''}
                            <button type="button" class="btn btn-secondary" onclick="taskValidation.hideValidationResult()">
                                关闭
                            </button>
                            ${isPassed ?
                                `<button type="button" class="btn btn-success" onclick="taskValidation.proceedToImport(${this.currentTaskId})">
                                    继续导入
                                </button>` : ''}
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', resultHtml);
    }

    /**
     * 生成错误统计HTML
     */
    generateErrorStatisticsHtml(errorStatistics) {
        if (!errorStatistics || Object.keys(errorStatistics).length === 0) {
            return '';
        }

        let html = '<div class="mb-3"><h6>错误统计</h6><div class="row">';

        for (const [errorType, count] of Object.entries(errorStatistics)) {
            html += `
                <div class="col-md-4 mb-2">
                    <div class="card">
                        <div class="card-body text-center">
                            <h6 class="card-title">${errorType}</h6>
                            <h5 class="text-danger">${count}</h5>
                        </div>
                    </div>
                </div>
            `;
        }

        html += '</div></div>';
        return html;
    }

    /**
     * 下载错误报告
     */
    async downloadErrorReport(taskId) {
        try {
            const response = await fetch(`${this.baseUrl}/tasks/${taskId}/error-report`);

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `validation_errors_task_${taskId}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                this.showSuccess('错误报告下载成功');
            } else {
                const result = await response.json();
                this.showError('下载失败: ' + result.message);
            }

        } catch (error) {
            console.error('下载错误报告失败:', error);
            this.showError('下载错误报告失败: ' + error.message);
        }
    }

    /**
     * 取消验证
     */
    async cancelValidation(taskId) {
        try {
            const response = await fetch(`${this.baseUrl}/tasks/${taskId}/validation`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
                this.stopProgressMonitoring();
                this.hideValidationProgress();
                this.showSuccess('验证已取消');
            } else {
                this.showError('取消验证失败: ' + result.message);
            }

        } catch (error) {
            console.error('取消验证失败:', error);
            this.showError('取消验证失败: ' + error.message);
        }
    }

    /**
     * 重新验证
     */
    async retryValidation(taskId) {
        this.hideValidationResult();
        await this.startValidation(taskId);
    }

    /**
     * 继续导入
     */
    proceedToImport(taskId) {
        this.hideValidationResult();

        // 如果没有传递taskId，使用当前任务ID
        const targetTaskId = taskId || this.currentTaskId;

        if (!targetTaskId) {
            this.showError('无法获取任务ID，请重新选择任务');
            return;
        }

        console.log('继续导入 - 任务ID:', targetTaskId);

        // 调用导入功能
        if (typeof startImport === 'function') {
            startImport(targetTaskId);
        } else {
            this.showInfo('请在任务列表中点击"导入"按钮继续');
        }
    }

    /**
     * 隐藏验证进度界面
     */
    hideValidationProgress() {
        const modal = document.getElementById('validation-progress-modal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 隐藏验证结果界面
     */
    hideValidationResult() {
        const modal = document.getElementById('validation-result-modal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * 显示信息消息
     */
    showInfo(message) {
        this.showToast(message, 'info');
    }

    /**
     * 显示Toast消息
     */
    showToast(message, type = 'info') {
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0" 
                 role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // 自动显示和隐藏Toast
        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement);
        toast.show();

        // 3秒后自动移除
        setTimeout(() => {
            if (toastElement.parentNode) {
                toastElement.remove();
            }
        }, 3000);
    }
}

// 创建全局实例
const taskValidation = new TaskValidationManager();
