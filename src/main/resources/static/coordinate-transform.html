<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>坐标转换工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #218838;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .coord-input {
            display: flex;
            gap: 10px;
        }
        .coord-input input {
            flex: 1;
        }
        .transform-pairs {
            margin-top: 15px;
        }
        .pair-category {
            margin-bottom: 20px;
        }
        .pair-category h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
            font-size: 16px;
        }
        .pair {
            background-color: #e3f2fd;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 8px;
            font-weight: 500;
            border-left: 4px solid #1976d2;
        }
        .pair:last-child {
            margin-bottom: 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #c3e6cb;
            margin-bottom: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
            margin-bottom: 15px;
        }
        .result-section {
            margin-top: 30px;
        }
        .result-section h2 {
            color: #333;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 10px;
        }
        #resultContainer {
            min-height: 100px;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ 坐标转换工具</h1>

        <!-- 导航菜单 -->
        <div style="margin-bottom: 30px; padding: 15px; background-color: #ecf0f1; border-radius: 5px;">
            <h3>功能导航</h3>
            <a href="index.html" style="margin-right: 20px; color: #28a745; text-decoration: none;">🏠 基础上传</a>
            <a href="template-upload.html" style="margin-right: 20px; color: #28a745; text-decoration: none;">📋 模板上传</a>
            <a href="coordinate-transform.html" style="color: #28a745; text-decoration: none; font-weight: bold;">🗺️ 坐标转换</a>
        </div>

        <!-- 坐标转换区域 -->
        <div class="section">
            <h2>🔄 坐标系转换</h2>
            <div class="form-group">
                <label>源坐标系:</label>
                <select id="sourceCoordSystem" onchange="updateCoordSystemInfo()">
                    <option value="CGCS2000">CGCS2000 (中国大地坐标系2000 - 经纬度)</option>
                    <option value="CGCS2000XY">CGCS2000XY (中国大地坐标系2000 - 投影坐标)</option>
                    <option value="WenZhou2000">WenZhou2000 (温州2000坐标系 - 投影坐标)</option>
                    <option value="WenZhouCity">WenZhouCity (温州城市坐标系 - 投影坐标)</option>
                    <option value="Beijing1954">Beijing1954 (北京1954坐标系 - 投影坐标)</option>
                </select>
            </div>
            <div class="form-group">
                <label>目标坐标系:</label>
                <select id="targetCoordSystem" onchange="updateCoordSystemInfo()">
                    <option value="CGCS2000">CGCS2000 (中国大地坐标系2000 - 经纬度)</option>
                    <option value="CGCS2000XY">CGCS2000XY (中国大地坐标系2000 - 投影坐标)</option>
                    <option value="WenZhou2000">WenZhou2000 (温州2000坐标系 - 投影坐标)</option>
                    <option value="WenZhouCity">WenZhouCity (温州城市坐标系 - 投影坐标)</option>
                    <option value="Beijing1954">Beijing1954 (北京1954坐标系 - 投影坐标)</option>
                </select>
            </div>

            <!-- 坐标系信息提示 -->
            <div id="coordSystemInfo" class="form-group" style="background-color: #e3f2fd; padding: 15px; border-radius: 4px; margin: 15px 0;">
                <small id="coordSystemInfoText">请选择源坐标系和目标坐标系</small>
            </div>

            <div class="form-group">
                <label>输入坐标:</label>
                <div class="coord-input">
                    <input type="number" id="inputX" placeholder="X坐标 (经度)" step="any">
                    <input type="number" id="inputY" placeholder="Y坐标 (纬度)" step="any">
                </div>
                <small style="color: #666; margin-top: 5px; display: block;">
                    <span id="coordInputHint">请根据源坐标系类型输入相应的坐标值</span>
                </small>
            </div>

            <!-- 坐标示例 -->
            <div class="form-group">
                <label>坐标示例:</label>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; font-size: 13px;">
                    <div style="margin-bottom: 8px;"><strong>经纬度坐标 (CGCS2000):</strong> X=120.672, Y=28.0</div>
                    <div style="margin-bottom: 8px;"><strong>温州投影坐标 (WenZhou2000):</strong> X=337641.456, Y=3379401.623</div>
                    <div style="margin-bottom: 8px;"><strong>国家投影坐标 (CGCS2000XY):</strong> X=566100.0, Y=3098623.7</div>
                    <div><strong>温州城市坐标 (WenZhouCity):</strong> X=338000, Y=3380000</div>
                </div>
            </div>

            <div class="form-group">
                <button onclick="autoDetectCoordType()" style="background-color: #17a2b8; margin-right: 10px;">🔍 自动检测坐标类型</button>
                <button onclick="transformCoordinate()" style="background-color: #28a745; margin-right: 10px;">🔄 转换坐标</button>
                <button onclick="clearInputs()" style="background-color: #6c757d;">🗑️ 清空输入</button>
            </div>
        </div>

        <!-- 支持的转换对 -->
        <div class="section">
            <h2>📋 支持的转换对</h2>
            <div class="transform-pairs">
                <div class="pair-category">
                    <h4>🔄 直接转换对</h4>
                    <div class="pair">WenZhou2000 → WenZhouCity (温州2000 → 温州城市)</div>
                    <div class="pair">Beijing1954 → WenZhouCity (北京1954 → 温州城市)</div>
                    <div class="pair">WenZhou2000 → CGCS2000 (温州2000 → 国家经纬度)</div>
                    <div class="pair">CGCS2000XY → CGCS2000 (国家投影 → 国家经纬度)</div>
                </div>
                <div class="pair-category">
                    <h4>↩️ 反向转换对</h4>
                    <div class="pair">WenZhouCity → WenZhou2000 (温州城市 → 温州2000)</div>
                    <div class="pair">WenZhouCity → Beijing1954 (温州城市 → 北京1954)</div>
                    <div class="pair">CGCS2000 → WenZhou2000 (国家经纬度 → 温州2000)</div>
                    <div class="pair">CGCS2000 → CGCS2000XY (国家经纬度 → 国家投影)</div>
                </div>
                <div class="pair-category">
                    <h4>🎯 常用转换场景</h4>
                    <div class="pair" style="background-color: #e8f5e8;">经纬度数据 (120.672, 28.0) → 使用 CGCS2000</div>
                    <div class="pair" style="background-color: #fff3e0;">温州投影坐标 (337641, 3379401) → 使用 WenZhou2000</div>
                    <div class="pair" style="background-color: #f3e5f5;">国家投影坐标 (566100, 3098623) → 使用 CGCS2000XY</div>
                    <div class="pair" style="background-color: #e1f5fe;">温州城市坐标 (338000, 3380000) → 使用 WenZhouCity</div>
                </div>
            </div>
        </div>

        <!-- 批量转换区域 -->
        <div class="section">
            <h2>📊 批量坐标转换</h2>
            <div class="form-group">
                <label>批量坐标输入 (每行一个坐标对，格式: X,Y):</label>
                <textarea id="batchCoords" rows="8" placeholder="例如:
116.3974, 39.9093
121.4737, 31.2304
113.2644, 23.1291"></textarea>
            </div>
            <div class="form-group">
                <button onclick="batchTransform()">批量转换</button>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div class="result-section">
            <h2>📋 转换结果</h2>
            <div id="resultContainer">
                <p>等待转换结果...</p>
            </div>
        </div>
    </div>

    <script>
        // 坐标系信息配置
        const coordSystemInfo = {
            'CGCS2000': {
                type: '经纬度坐标',
                ellipsoid: 'CGCS2000',
                centralMeridian: '120°',
                usage: '中国大地坐标系2000，经纬度坐标',
                range: '全国通用',
                example: 'X=120.672, Y=28.0'
            },
            'CGCS2000XY': {
                type: '投影坐标',
                ellipsoid: 'CGCS2000',
                centralMeridian: '120°',
                usage: '中国大地坐标系2000，投影坐标',
                range: '全国通用',
                example: 'X=566100.0, Y=3098623.7'
            },
            'WenZhou2000': {
                type: '投影坐标',
                ellipsoid: 'CGCS2000',
                centralMeridian: '120.666667°',
                usage: '温州2000坐标系，投影坐标',
                range: '温州地区',
                example: 'X=337641.456, Y=3379401.623'
            },
            'WenZhouCity': {
                type: '投影坐标',
                ellipsoid: 'Beijing1954',
                centralMeridian: '120.666667°',
                usage: '温州城市坐标系，投影坐标',
                range: '温州城区',
                example: 'X=338000, Y=3380000'
            },
            'Beijing1954': {
                type: '投影坐标',
                ellipsoid: 'Beijing1954',
                centralMeridian: '120°',
                usage: '北京1954坐标系，投影坐标',
                range: '历史坐标系',
                example: 'X=338500, Y=3380500'
            }
        };

        // 更新坐标系信息显示
        function updateCoordSystemInfo() {
            const sourceCoord = document.getElementById('sourceCoordSystem').value;
            const targetCoord = document.getElementById('targetCoordSystem').value;
            const infoElement = document.getElementById('coordSystemInfoText');
            const hintElement = document.getElementById('coordInputHint');

            if (sourceCoord && targetCoord) {
                const sourceInfo = coordSystemInfo[sourceCoord];
                const targetInfo = coordSystemInfo[targetCoord];

                infoElement.innerHTML = `
                    <strong>源坐标系:</strong> ${sourceInfo.usage} (${sourceInfo.type})<br>
                    <strong>目标坐标系:</strong> ${targetInfo.usage} (${targetInfo.type})<br>
                    <strong>转换方向:</strong> ${sourceCoord} → ${targetCoord}
                `;

                hintElement.innerHTML = `请输入${sourceInfo.type}格式的坐标，示例: ${sourceInfo.example}`;

                // 更新输入框占位符
                if (sourceInfo.type === '经纬度坐标') {
                    document.getElementById('inputX').placeholder = 'X坐标 (经度)';
                    document.getElementById('inputY').placeholder = 'Y坐标 (纬度)';
                } else {
                    document.getElementById('inputX').placeholder = 'X坐标 (投影)';
                    document.getElementById('inputY').placeholder = 'Y坐标 (投影)';
                }
            } else {
                infoElement.innerHTML = '请选择源坐标系和目标坐标系';
                hintElement.innerHTML = '请根据源坐标系类型输入相应的坐标值';
            }
        }

        // 自动检测坐标类型
        function autoDetectCoordType() {
            const inputX = parseFloat(document.getElementById('inputX').value);
            const inputY = parseFloat(document.getElementById('inputY').value);

            if (isNaN(inputX) || isNaN(inputY)) {
                showError('请先输入坐标值');
                return;
            }

            let detectedType = 'UNKNOWN';
            let confidence = '';

            // 检查是否为经纬度坐标
            if (inputX >= -180 && inputX <= 180 && inputY >= -90 && inputY <= 90) {
                detectedType = 'CGCS2000';
                confidence = '高';
            }
            // 检查是否为温州地区投影坐标
            else if (inputX >= 200000 && inputX <= 500000 && inputY >= 3000000 && inputY <= 3500000) {
                detectedType = 'WenZhou2000';
                confidence = '高';
            }
            // 检查是否为国家投影坐标
            else if (inputX >= -2000000 && inputX <= 2000000 && inputY >= 1000000 && inputY <= 6000000) {
                detectedType = 'CGCS2000XY';
                confidence = '中';
            }

            if (detectedType !== 'UNKNOWN') {
                document.getElementById('sourceCoordSystem').value = detectedType;
                updateCoordSystemInfo();
                showSuccess(`🔍 自动检测结果：\n坐标类型: ${detectedType}\n置信度: ${confidence}\n建议: ${coordSystemInfo[detectedType].usage}`);
            } else {
                showError('🔍 无法自动检测坐标类型，请手动选择坐标系');
            }
        }

        // 单个坐标转换
        async function transformCoordinate() {
            const inputX = parseFloat(document.getElementById('inputX').value);
            const inputY = parseFloat(document.getElementById('inputY').value);
            const sourceCoordSystem = document.getElementById('sourceCoordSystem').value;
            const targetCoordSystem = document.getElementById('targetCoordSystem').value;

            if (isNaN(inputX) || isNaN(inputY)) {
                showError('请输入有效的坐标值');
                return;
            }

            if (sourceCoordSystem === targetCoordSystem) {
                showError('源坐标系和目标坐标系相同，无需转换');
                return;
            }

            const wkt = `POINT(${inputX} ${inputY})`;

            try {
                const response = await fetch('/api/coordinate/transform', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        wkt: wkt,
                        sourceCoordSystem: sourceCoordSystem,
                        targetCoordSystem: targetCoordSystem
                    })
                });

                if (response.ok) {
                    const result = await response.text();

                    // 检查返回的是否是JSON格式（错误情况）
                    if (result.startsWith('{') && result.includes('transformedWkt')) {
                        try {
                            const jsonResult = JSON.parse(result);
                            if (jsonResult.success && jsonResult.transformedWkt) {
                                const transformedWkt = jsonResult.transformedWkt;
                                displayTransformResult(wkt, transformedWkt, sourceCoordSystem, targetCoordSystem);
                            } else {
                                showError(`❌ 转换失败: ${jsonResult.message || '未知错误'}`);
                            }
                        } catch (e) {
                            showError(`❌ 解析响应失败: ${result}`);
                        }
                    } else {
                        // 正常的WKT格式结果
                        displayTransformResult(wkt, result, sourceCoordSystem, targetCoordSystem);
                    }
                } else {
                    const errorText = await response.text();
                    showError(`❌ 转换失败: ${errorText}`);
                }
            } catch (error) {
                showError(`❌ 请求失败: ${error.message}`);
            }
        }

        // 批量坐标转换
        async function batchTransform() {
            const batchText = document.getElementById('batchCoords').value;
            if (!batchText.trim()) {
                showError('请输入批量坐标数据');
                return;
            }

            const sourceCoordSystem = document.getElementById('sourceCoordSystem').value;
            const targetCoordSystem = document.getElementById('targetCoordSystem').value;

            if (sourceCoordSystem === targetCoordSystem) {
                showError('源坐标系和目标坐标系相同，无需转换');
                return;
            }

            // 解析坐标数据
            const lines = batchText.trim().split('\n');
            const coordinates = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (line) {
                    const parts = line.split(',');
                    if (parts.length >= 2) {
                        const x = parseFloat(parts[0].trim());
                        const y = parseFloat(parts[1].trim());
                        if (!isNaN(x) && !isNaN(y)) {
                            coordinates.push({ x, y });
                        }
                    }
                }
            }

            if (coordinates.length === 0) {
                showError('没有找到有效的坐标数据');
                return;
            }

            try {
                const results = [];
                let successCount = 0;

                for (const coord of coordinates) {
                    const wkt = `POINT(${coord.x} ${coord.y})`;

                    const response = await fetch('/api/coordinate/transform', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            wkt: wkt,
                            sourceCoordSystem: sourceCoordSystem,
                            targetCoordSystem: targetCoordSystem
                        })
                    });

                    if (response.ok) {
                        const result = await response.text();

                        // 检查是否是JSON格式的错误响应
                        if (result.startsWith('{') && result.includes('transformedWkt')) {
                            try {
                                const jsonResult = JSON.parse(result);
                                if (jsonResult.success && jsonResult.transformedWkt) {
                                    const transformedWkt = jsonResult.transformedWkt;
                                    const success = transformedWkt !== wkt;
                                    if (success) successCount++;
                                    results.push({
                                        input: wkt,
                                        output: transformedWkt,
                                        success: success
                                    });
                                } else {
                                    results.push({
                                        input: wkt,
                                        output: `错误: ${jsonResult.message || '转换失败'}`,
                                        success: false
                                    });
                                }
                            } catch (e) {
                                results.push({
                                    input: wkt,
                                    output: `解析错误: ${result}`,
                                    success: false
                                });
                            }
                        } else {
                            // 正常的WKT格式结果
                            const success = result !== wkt;
                            if (success) successCount++;
                            results.push({
                                input: wkt,
                                output: result,
                                success: success
                            });
                        }
                    } else {
                        const errorText = await response.text();
                        results.push({
                            input: wkt,
                            output: `HTTP错误: ${errorText}`,
                            success: false
                        });
                    }
                }

                const sourceInfo = coordSystemInfo[sourceCoordSystem];
                const targetInfo = coordSystemInfo[targetCoordSystem];

                showSuccess(`✅ 批量转换完成！\n\n📊 转换统计: ${successCount}/${results.length} 个坐标转换成功\n📍 源坐标系: ${sourceInfo.usage}\n📍 目标坐标系: ${targetInfo.usage}\n\n详细结果请查看下方列表`);

                displayBatchResult(results, sourceInfo, targetInfo);

            } catch (error) {
                showError('❌ 批量转换失败: ' + error.message);
            }
        }

        // 显示批量转换结果
        function displayBatchResult(results, sourceInfo, targetInfo) {
            const resultContainer = document.getElementById('resultContainer');
            let html = `
                <div class="success">批量转换结果</div>
                <div style="margin: 15px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                    <strong>转换配置:</strong><br>
                    源坐标系: ${sourceInfo.usage}<br>
                    目标坐标系: ${targetInfo.usage}
                </div>
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background-color: #f8f9fa; position: sticky; top: 0;">
                            <tr>
                                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">序号</th>
                                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">原始坐标</th>
                                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">转换结果</th>
                                <th style="padding: 10px; border: 1px solid #ddd; text-align: center;">状态</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            results.forEach((result, index) => {
                const statusIcon = result.success ? '✅' : '❌';
                const statusText = result.success ? '成功' : '失败';
                html += `
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">${index + 1}</td>
                        <td style="padding: 8px; border: 1px solid #ddd; font-family: monospace;">${result.input}</td>
                        <td style="padding: 8px; border: 1px solid #ddd; font-family: monospace;">${result.output}</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${statusIcon} ${statusText}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            resultContainer.innerHTML = html;
        }

        // 清空输入
        function clearInputs() {
            document.getElementById('inputX').value = '';
            document.getElementById('inputY').value = '';
            document.getElementById('batchCoords').value = '';
            document.getElementById('resultContainer').innerHTML = '<p>等待转换结果...</p>';
        }

        // 显示转换结果
        function displayTransformResult(originalWkt, transformedWkt, sourceCoordSystem, targetCoordSystem) {
            const sourceInfo = coordSystemInfo[sourceCoordSystem];
            const targetInfo = coordSystemInfo[targetCoordSystem];

            // 解析坐标
            const originalCoords = parseWktCoordinates(originalWkt);
            const transformedCoords = parseWktCoordinates(transformedWkt);

            const resultContainer = document.getElementById('resultContainer');
            resultContainer.innerHTML = `
                <div class="success">✅ 坐标转换成功！</div>
                <div style="margin: 20px 0; padding: 20px; background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div style="padding: 15px; background-color: #e3f2fd; border-radius: 6px;">
                            <h4 style="margin: 0 0 10px 0; color: #1976d2;">📍 原始坐标</h4>
                            <div style="font-family: monospace; font-size: 14px; margin-bottom: 8px;">
                                <strong>X:</strong> ${originalCoords.x}<br>
                                <strong>Y:</strong> ${originalCoords.y}
                            </div>
                            <div style="font-size: 12px; color: #666;">
                                <strong>坐标系:</strong> ${sourceInfo.usage}<br>
                                <strong>类型:</strong> ${sourceInfo.type}
                            </div>
                        </div>
                        <div style="padding: 15px; background-color: #e8f5e8; border-radius: 6px;">
                            <h4 style="margin: 0 0 10px 0; color: #2e7d32;">📍 转换结果</h4>
                            <div style="font-family: monospace; font-size: 14px; margin-bottom: 8px;">
                                <strong>X:</strong> ${transformedCoords.x}<br>
                                <strong>Y:</strong> ${transformedCoords.y}
                            </div>
                            <div style="font-size: 12px; color: #666;">
                                <strong>坐标系:</strong> ${targetInfo.usage}<br>
                                <strong>类型:</strong> ${targetInfo.type}
                            </div>
                        </div>
                    </div>
                    <div style="padding: 10px; background-color: #fff3e0; border-radius: 4px; font-size: 13px;">
                        <strong>🔄 转换方向:</strong> ${sourceCoordSystem} → ${targetCoordSystem}
                    </div>
                </div>
            `;
        }

        // 解析WKT坐标
        function parseWktCoordinates(wkt) {
            try {
                const match = wkt.match(/POINT\(([^)]+)\)/);
                if (match) {
                    const coords = match[1].split(' ');
                    return {
                        x: parseFloat(coords[0]).toFixed(6),
                        y: parseFloat(coords[1]).toFixed(6)
                    };
                }
            } catch (e) {
                console.error('解析WKT失败:', e);
            }
            return { x: 'N/A', y: 'N/A' };
        }

        // 显示成功消息
        function showSuccess(message) {
            const resultContainer = document.getElementById('resultContainer');
            resultContainer.innerHTML = `<div class="success" style="white-space: pre-line;">${message}</div>`;
        }

        // 显示错误消息
        function showError(message) {
            const resultContainer = document.getElementById('resultContainer');
            resultContainer.innerHTML = `<div class="error" style="white-space: pre-line;">${message}</div>`;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateCoordSystemInfo();
        });
    </script>
</body>
</html>
