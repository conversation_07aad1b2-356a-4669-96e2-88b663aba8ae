<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GIS导入任务管理</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        .task-status-badge {
            font-size: 0.75em;
            font-weight: 600;
            padding: 0.35em 0.65em;
            border-radius: 0.375rem;
        }

        .progress-mini {
            height: 6px;
            border-radius: 3px;
        }

        .card-hover {
            transition: all 0.3s ease;
            border: 1px solid #e3e6f0;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #5a5c69;
        }

        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .card-header {
            border-bottom: 1px solid #e3e6f0;
        }

        .card-footer {
            border-top: 1px solid #e3e6f0;
            padding: 0.75rem 1rem;
        }

        .text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .shadow-sm {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        /* 模板搜索框样式 */
        .template-search-container {
            position: relative;
        }

        .template-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1050;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            max-height: 250px;
            overflow-y: auto;
        }

        .template-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
        }

        .template-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .template-dropdown .dropdown-item:last-child {
            border-bottom: none;
        }

        .template-dropdown .dropdown-item strong {
            color: #495057;
        }

        .template-dropdown .dropdown-item small {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tasks"></i> GIS导入任务管理</h2>
                    <div>
                        <button class="btn btn-primary" onclick="showCreateTaskModal()">
                            <i class="fas fa-plus"></i> 创建任务
                        </button>
                        <button class="btn btn-outline-secondary" onclick="refreshTaskList()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 筛选器 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">数据状态</label>
                                <select id="statusFilter" class="form-select" onchange="filterTasks()">
                                    <option value="">全部状态</option>
                                    <option value="0">未导入</option>
                                    <option value="1">数据未检查</option>
                                    <option value="2">数据已导入</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">导入格式</label>
                                <select id="formatFilter" class="form-select" onchange="filterTasks()">
                                    <option value="">全部格式</option>
                                    <option value="1">SHP格式</option>
                                    <option value="2">Excel格式</option>
                                    <option value="3">CAD格式</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">搜索</label>
                                <input type="text" id="searchInput" class="form-control" placeholder="搜索任务名称..." onkeyup="filterTasks()">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-outline-primary" onclick="clearFilters()">
                                        <i class="fas fa-eraser"></i> 清除筛选
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div id="taskList" class="row">
                    <!-- 任务卡片将在这里动态生成 -->
                </div>

                <!-- 加载指示器 -->
                <div id="loadingIndicator" class="text-center py-5" style="display: none;">
                    <div class="d-flex justify-content-center align-items-center">
                        <div class="spinner-border text-primary me-3" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div>
                            <h5 class="mb-1">正在加载任务列表</h5>
                            <p class="text-muted mb-0">请稍候...</p>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-5" style="display: none;">
                    <div class="empty-state-container">
                        <i class="fas fa-tasks fa-4x text-muted mb-4" style="opacity: 0.5;"></i>
                        <h4 class="text-muted mb-3">暂无导入任务</h4>
                        <p class="text-muted mb-4">您还没有创建任何导入任务</p>
                        <button class="btn btn-primary btn-lg" onclick="showCreateTaskModal()">
                            <i class="fas fa-plus me-2"></i>创建第一个任务
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建任务模态框 -->
    <div class="modal fade" id="createTaskModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建导入任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createTaskForm">
                        <div class="mb-3">
                            <label class="form-label">任务名称</label>
                            <input type="text" class="form-control" id="taskName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">选择文件</label>
                            <input type="file" class="form-control" id="taskFile" accept=".zip,.shp,.xlsx,.xls,.dwg,.dxf" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">选择模板</label>
                            <div class="template-search-container">
                                <input type="text" class="form-control" id="templateSearch" placeholder="搜索模板名称或输入模板ID..." autocomplete="off">
                                <input type="hidden" id="templateId" required>
                                <div id="templateDropdown" class="template-dropdown" style="display: none;">
                                    <!-- 模板选项将动态加载 -->
                                </div>
                            </div>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i>
                                可以搜索模板名称、ID或类型，支持直接输入模板ID
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createTask()">创建任务</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 任务验证脚本 -->
    <script src="js/task-validation.js"></script>

    <script>
        // 全局变量
        let allTasks = [];
        let filteredTasks = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTemplates();
            refreshTaskList();
        });

        /**
         * 刷新任务列表
         */
        async function refreshTaskList() {
            showLoading(true);

            try {
                console.log('开始获取任务列表...');
                const response = await fetch('/api/file-import/tasks');
                const result = await response.json();

                console.log('任务列表API响应:', result);

                // 适配framework的Result格式：{code: "200", msg: "执行成功", data: [...]}
                if (result.code === "200" || result.code === 200) {
                    // framework的Result格式，数据在result.data中
                    allTasks = result.data || [];
                    filteredTasks = [...allTasks];

                    console.log('获取到任务数量:', allTasks.length);
                    if (allTasks.length > 0) {
                        console.log('第一个任务示例:', allTasks[0]);
                    }

                    renderTaskList();
                } else {
                    console.error('API返回错误:', result.msg || result.message);
                    showError('加载任务列表失败: ' + (result.msg || result.message || '未知错误'));
                }

            } catch (error) {
                console.error('加载任务列表失败:', error);
                showError('加载任务列表失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        /**
         * 渲染任务列表
         */
        function renderTaskList() {
            const taskListContainer = document.getElementById('taskList');
            const emptyState = document.getElementById('emptyState');

            if (filteredTasks.length === 0) {
                taskListContainer.innerHTML = '';
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';

            const tasksHtml = filteredTasks.map(task => generateTaskCard(task)).join('');
            taskListContainer.innerHTML = tasksHtml;
        }

        /**
         * 生成任务卡片HTML
         */
        function generateTaskCard(task) {
            const statusInfo = getStatusInfo(task.dataStatus);
            const formatInfo = getFormatInfo(task.importFormat);
            const progressPercentage = task.progressPercentage || 0;

            console.log('生成任务卡片:', {
                taskId: task.id,
                taskName: task.taskName,
                dataStatus: task.dataStatus,
                importFormat: task.importFormat,
                statusInfo: statusInfo,
                formatInfo: formatInfo
            });

            return `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card card-hover h-100 shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center py-2">
                            <h6 class="mb-0 text-truncate" title="${task.taskName}">${task.taskName}</h6>
                            <span class="badge ${statusInfo.class} task-status-badge ms-2">${statusInfo.text}</span>
                        </div>
                        <div class="card-body">
                            <!-- 基本信息 -->
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="${formatInfo.icon} ${formatInfo.color} me-2"></i>
                                    <span class="text-muted small">${formatInfo.text}</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock text-muted me-2"></i>
                                    <span class="text-muted small">${formatDate(task.importTime)}</span>
                                </div>
                            </div>

                            <!-- 文件信息 -->
                            ${task.filePath ? `
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-folder-open me-1"></i>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" title="${task.filePath}">
                                            ${task.filePath.split('/').pop()}
                                        </span>
                                    </small>
                                </div>
                            ` : ''}

                            <!-- 文件大小 -->
                            ${task.fileSize ? `
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-weight-hanging me-1"></i>
                                        ${formatFileSize(task.fileSize)}
                                    </small>
                                </div>
                            ` : ''}

                            <!-- 进度信息 -->
                            ${task.recordCount ? `
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">记录数: ${task.recordCount}</small>
                                        ${task.processedCount ? `<small class="text-info">${progressPercentage.toFixed(1)}%</small>` : ''}
                                    </div>
                                    ${task.processedCount ? `
                                        <div class="progress progress-mini mt-1">
                                            <div class="progress-bar bg-info" style="width: ${progressPercentage}%"></div>
                                        </div>
                                        <small class="text-muted">${task.processedCount}/${task.recordCount}</small>
                                    ` : ''}
                                </div>
                            ` : ''}

                            <!-- 错误信息 -->
                            ${task.errorCount > 0 ? `
                                <div class="mb-2">
                                    <small class="text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        ${task.errorCount} 个错误
                                    </small>
                                </div>
                            ` : ''}

                            <!-- 成功率 -->
                            ${task.successRate ? `
                                <div class="mb-2">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>
                                        成功率: ${task.successRate}%
                                    </small>
                                </div>
                            ` : ''}

                            <!-- 创建者信息 -->
                            ${task.createdBy ? `
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        ${task.createdBy}
                                    </small>
                                </div>
                            ` : ''}
                        </div>
                        <div class="card-footer bg-light">
                            <div class="btn-group btn-group-sm w-100" role="group">
                                ${generateActionButtons(task)}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        /**
         * 生成操作按钮
         */
        function generateActionButtons(task) {
            let buttons = [];
            let statusKey = task.dataStatus;

            // 处理不同类型的状态值
            if (typeof task.dataStatus === 'number') {
                switch (task.dataStatus) {
                    case 0: statusKey = 'NOT_IMPORTED'; break;
                    case 1: statusKey = 'DATA_UNCHECKED'; break;
                    case 2: statusKey = 'DATA_IMPORTED'; break;
                }
            }

            // 根据任务状态显示不同的按钮
            switch (statusKey) {
                case 'NOT_IMPORTED':
                case 0:
                    buttons.push(`
                        <button class="btn btn-warning btn-sm" onclick="taskValidation.startValidation(${task.id})" title="开始数据检查">
                            <i class="fas fa-check-circle"></i> 数据检查
                        </button>
                    `);
                    break;

                case 'DATA_UNCHECKED':
                case 1:
                    buttons.push(`
                        <button class="btn btn-success btn-sm" onclick="startImport(${task.id})" title="开始导入数据">
                            <i class="fas fa-upload"></i> 导入数据
                        </button>
                    `);
                    buttons.push(`
                        <button class="btn btn-outline-warning btn-sm" onclick="taskValidation.startValidation(${task.id})" title="重新检查数据">
                            <i class="fas fa-redo"></i> 重新检查
                        </button>
                    `);
                    break;

                case 'DATA_IMPORTED':
                case 2:
                    buttons.push(`
                        <button class="btn btn-info btn-sm" onclick="viewImportResult(${task.id})" title="查看导入结果">
                            <i class="fas fa-eye"></i> 查看结果
                        </button>
                    `);
                    break;

                default:
                    buttons.push(`
                        <button class="btn btn-secondary btn-sm" disabled title="状态未知">
                            <i class="fas fa-question-circle"></i> 未知状态
                        </button>
                    `);
                    break;
            }

            // 详情按钮
            buttons.push(`
                <button class="btn btn-outline-primary btn-sm" onclick="viewTaskDetails(${task.id})" title="查看任务详情">
                    <i class="fas fa-info-circle"></i> 详情
                </button>
            `);

            // 如果有验证结果且有错误，显示下载错误报告按钮
            if (task.validationResult && task.errorCount > 0) {
                buttons.push(`
                    <button class="btn btn-outline-warning btn-sm" onclick="taskValidation.downloadErrorReport(${task.id})" title="下载错误报告">
                        <i class="fas fa-download"></i> 错误报告
                    </button>
                `);
            }

            // 删除按钮
            buttons.push(`
                <button class="btn btn-outline-danger btn-sm" onclick="deleteTask(${task.id})" title="删除任务">
                    <i class="fas fa-trash"></i> 删除
                </button>
            `);

            return buttons.join('');
        }

        /**
         * 获取状态信息
         */
        function getStatusInfo(status) {
            // 处理不同类型的状态值
            let statusKey = status;

            // 如果是数字，转换为对应的枚举名称
            if (typeof status === 'number') {
                switch (status) {
                    case 0: statusKey = 'NOT_IMPORTED'; break;
                    case 1: statusKey = 'DATA_UNCHECKED'; break;
                    case 2: statusKey = 'DATA_IMPORTED'; break;
                }
            }

            // 统一处理枚举名称
            switch (statusKey) {
                case 'NOT_IMPORTED':
                case 0:
                    return { text: '未导入', class: 'bg-secondary text-white' };
                case 'DATA_UNCHECKED':
                case 1:
                    return { text: '数据已检查', class: 'bg-warning text-dark' };
                case 'DATA_IMPORTED':
                case 2:
                    return { text: '数据已导入', class: 'bg-success text-white' };
                default:
                    console.warn('未知状态:', status, '类型:', typeof status);
                    return { text: '未知状态', class: 'bg-dark text-white' };
            }
        }

        /**
         * 获取格式信息
         */
        function getFormatInfo(format) {
            // 处理不同类型的格式值
            let formatKey = format;

            // 如果是数字，转换为对应的枚举名称
            if (typeof format === 'number') {
                switch (format) {
                    case 1: formatKey = 'SHP'; break;
                    case 2: formatKey = 'EXCEL'; break;
                    case 3: formatKey = 'CAD'; break;
                }
            }

            // 统一处理枚举名称
            switch (formatKey) {
                case 'SHP':
                case 1:
                    return { text: 'SHP格式', icon: 'fas fa-map', color: 'text-primary' };
                case 'EXCEL':
                case 2:
                    return { text: 'Excel格式', icon: 'fas fa-file-excel', color: 'text-success' };
                case 'CAD':
                case 3:
                    return { text: 'CAD格式', icon: 'fas fa-drafting-compass', color: 'text-info' };
                default:
                    console.warn('未知格式:', format, '类型:', typeof format);
                    return { text: '未知格式', icon: 'fas fa-file', color: 'text-muted' };
            }
        }

        /**
         * 格式化日期
         */
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        /**
         * 格式化文件大小
         */
        function formatFileSize(bytes) {
            if (!bytes || bytes === 0) return '0 B';

            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        /**
         * 筛选任务
         */
        function filterTasks() {
            const statusFilter = document.getElementById('statusFilter').value;
            const formatFilter = document.getElementById('formatFilter').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();

            filteredTasks = allTasks.filter(task => {
                const statusMatch = !statusFilter || task.dataStatus.toString() === statusFilter;
                const formatMatch = !formatFilter || task.importFormat.toString() === formatFilter;
                const searchMatch = !searchInput || task.taskName.toLowerCase().includes(searchInput);

                return statusMatch && formatMatch && searchMatch;
            });

            renderTaskList();
        }

        /**
         * 清除筛选
         */
        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('formatFilter').value = '';
            document.getElementById('searchInput').value = '';
            filteredTasks = [...allTasks];
            renderTaskList();
        }

        /**
         * 显示/隐藏加载指示器
         */
        function showLoading(show) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const taskList = document.getElementById('taskList');

            if (show) {
                loadingIndicator.style.display = 'block';
                taskList.style.display = 'none';
            } else {
                loadingIndicator.style.display = 'none';
                taskList.style.display = 'flex';
            }
        }

        /**
         * 显示创建任务模态框
         */
        function showCreateTaskModal() {
            const modal = new bootstrap.Modal(document.getElementById('createTaskModal'));
            modal.show();
        }

        // 全局变量存储模板列表
        let allTemplates = [];

        /**
         * 加载模板列表
         */
        async function loadTemplates() {
            try {
                console.log('开始加载模板列表...');

                // 调用获取模板列表的API
                const response = await fetch('/api/template-shapefile/templates');
                const result = await response.json();

                if (result.success && result.data) {
                    // console.log('获取到模板列表:', result.data);
                    allTemplates = result.data;

                    // 初始化模板搜索功能
                    initializeTemplateSearch();

                    console.log(`成功加载 ${result.data.length} 个模板`);
                } else {
                    console.error('获取模板列表失败:', result.message);
                    taskValidation.showError('获取模板列表失败: ' + (result.message || '未知错误'));
                }

            } catch (error) {
                console.error('加载模板列表失败:', error);
                taskValidation.showError('加载模板列表失败: ' + error.message);
            }
        }

        /**
         * 初始化模板搜索功能
         */
        function initializeTemplateSearch() {
            const searchInput = document.getElementById('templateSearch');
            const hiddenInput = document.getElementById('templateId');
            const dropdown = document.getElementById('templateDropdown');

            // 搜索输入事件
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                filterTemplates(searchTerm);
            });

            // 点击输入框显示下拉列表
            searchInput.addEventListener('focus', function() {
                filterTemplates(this.value.toLowerCase());
                dropdown.style.display = 'block';
            });

            // 点击外部隐藏下拉列表
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.template-search-container')) {
                    dropdown.style.display = 'none';
                }
            });

            // 初始显示所有模板
            filterTemplates('');
        }

        /**
         * 过滤模板列表
         */
        function filterTemplates(searchTerm) {
            const dropdown = document.getElementById('templateDropdown');
            dropdown.innerHTML = '';

            let filteredTemplates = allTemplates;

            if (searchTerm) {
                filteredTemplates = allTemplates.filter(template => {
                    const name = (template.nameZh || template.name || '').toLowerCase();
                    const id = template.id.toString();
                    const type = (template.templateType || '').toLowerCase();

                    return name.includes(searchTerm) ||
                           id.includes(searchTerm) ||
                           type.includes(searchTerm);
                });
            }

            if (filteredTemplates.length === 0) {
                const noResult = document.createElement('div');
                noResult.className = 'dropdown-item-text text-muted';
                noResult.textContent = '未找到匹配的模板';
                dropdown.appendChild(noResult);
            } else {
                filteredTemplates.forEach(template => {
                    const item = document.createElement('a');
                    item.className = 'dropdown-item';
                    item.href = '#';
                    item.innerHTML = `
                        <div>
                            <strong>${template.nameZh || template.name || `模板${template.id}`}</strong>
                            <small class="text-muted d-block">ID: ${template.id} | 类型: ${template.templateType || '未知'}</small>
                            ${template.tableName ? `<small class="text-info d-block">目标表: ${template.tableName}</small>` : ''}
                        </div>
                    `;

                    item.addEventListener('click', function(e) {
                        e.preventDefault();
                        selectTemplate(template);
                    });

                    dropdown.appendChild(item);
                });
            }

            dropdown.style.display = 'block';
        }

        /**
         * 选择模板
         */
        function selectTemplate(template) {
            const searchInput = document.getElementById('templateSearch');
            const hiddenInput = document.getElementById('templateId');
            const dropdown = document.getElementById('templateDropdown');

            searchInput.value = `${template.nameZh || template.name || `模板${template.id}`} (ID: ${template.id})`;
            hiddenInput.value = template.id;
            dropdown.style.display = 'none';

            console.log('选择了模板:', template);
        }

        /**
         * 创建任务
         */
        async function createTask() {
            try {
                // 验证表单
                const taskName = document.getElementById('taskName').value.trim();
                const templateId = document.getElementById('templateId').value;
                const fileInput = document.getElementById('taskFile');

                if (!taskName) {
                    taskValidation.showError('请输入任务名称');
                    return;
                }

                if (!templateId) {
                    taskValidation.showError('请选择模板');
                    return;
                }

                if (!fileInput.files.length) {
                    taskValidation.showError('请选择要上传的文件');
                    return;
                }

                // 验证模板ID是否有效
                const selectedTemplate = allTemplates.find(t => t.id.toString() === templateId);
                if (!selectedTemplate) {
                    taskValidation.showError('选择的模板无效，请重新选择');
                    return;
                }

                // 创建FormData
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                formData.append('templateId', templateId);
                formData.append('taskName', taskName);
                formData.append('createdBy', 'web_user'); // 可以从用户会话中获取

                console.log('创建任务参数:', {
                    taskName: taskName,
                    templateId: templateId,
                    template: selectedTemplate,
                    fileName: fileInput.files[0].name,
                    fileSize: fileInput.files[0].size
                });

                // 发送请求到统一的文件导入API
                const response = await fetch('/api/file-import/tasks', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                // 适配framework的Result格式
                if (result.code === "200" || result.code === 200) {
                    taskValidation.showSuccess('任务创建成功');
                    bootstrap.Modal.getInstance(document.getElementById('createTaskModal')).hide();

                    // 重置表单
                    document.getElementById('createTaskForm').reset();
                    document.getElementById('templateSearch').value = '';
                    document.getElementById('templateId').value = '';

                    // 刷新任务列表
                    refreshTaskList();
                } else {
                    taskValidation.showError('创建任务失败: ' + (result.msg || result.message || '未知错误'));
                }

            } catch (error) {
                console.error('创建任务失败:', error);
                taskValidation.showError('创建任务失败: ' + error.message);
            }
        }

        /**
         * 开始导入
         */
        async function startImport(taskId) {
            try {
                // 验证taskId参数
                if (!taskId || taskId === 'undefined' || taskId === undefined) {
                    taskValidation.showError('任务ID无效，请重新选择任务');
                    console.error('startImport调用时taskId无效:', taskId);
                    return;
                }

                // 确保taskId是数字类型
                const numericTaskId = parseInt(taskId);
                if (isNaN(numericTaskId)) {
                    taskValidation.showError('任务ID格式错误');
                    console.error('taskId不是有效数字:', taskId);
                    return;
                }

                console.log('开始导入数据 - 任务ID:', numericTaskId);

                // 1. 获取任务详细信息
                const taskDetails = await getTaskDetails(numericTaskId);
                if (!taskDetails) {
                    taskValidation.showError('获取任务信息失败');
                    return;
                }

                // 2. 检查任务状态
                const task = taskDetails.task;
                const template = taskDetails.template;

                if (!task) {
                    throw new Error('任务信息不完整');
                }

                const dataStatus = task.dataStatus;
                console.log('任务状态:', dataStatus, '任务对象:', task);

                // 如果数据状态为"未检查"，显示确认对话框
                if (task.dataStatus === 'DATA_UNCHECKED' || task.dataStatus === 1) {
                    const confirmed = await showImportConfirmDialog(task, template);
                    if (!confirmed) {
                        console.log('用户取消导入操作');
                        return;
                    }
                }

                // 3. 执行导入操作
                await executeImport(task, template);

            } catch (error) {
                console.error('导入过程出错:', error);
                taskValidation.showError('导入失败: ' + error.message);
            }
        }

        /**
         * 获取任务详细信息
         */
        async function getTaskDetails(taskId) {
            try {
                // 验证taskId参数
                if (!taskId || taskId === 'undefined' || taskId === undefined) {
                    throw new Error('任务ID不能为空或undefined');
                }

                const numericTaskId = parseInt(taskId);
                if (isNaN(numericTaskId)) {
                    throw new Error('任务ID必须是有效数字');
                }

                console.log('获取任务详细信息 - 任务ID:', numericTaskId);

                const response = await fetch(`/api/file-import/tasks/${numericTaskId}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                // 适配framework的Result格式
                if (result.code === "200" || result.code === 200) {
                    console.log('获取任务详细信息成功:', result.data);
                    return result.data;
                } else {
                    throw new Error(result.msg || result.message || '获取任务详细信息失败');
                }
            } catch (error) {
                console.error('获取任务详细信息失败:', error);
                throw error;
            }
        }

        /**
         * 显示导入确认对话框
         */
        function showImportConfirmDialog(task, template) {
            return new Promise((resolve) => {
                // 创建确认对话框
                const modalHtml = `
                    <div class="modal fade" id="importConfirmModal" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                        确认导入数据
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-info-circle"></i> 任务信息</h6>
                                        <p><strong>任务名称：</strong>${task.taskName || '未知'}</p>
                                        <p><strong>模板名称：</strong>${template ? template.nameZh : '未知'}</p>
                                        <p><strong>数据状态：</strong><span class="badge bg-warning">${task.dataStatus || '未检查'}</span></p>
                                        <p><strong>记录数量：</strong>${task.recordCount || 0} 条</p>
                                        <p><strong>文件路径：</strong><small>${task.filePath || '未知'}</small></p>
                                    </div>
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-question-circle"></i> 确认提示</h6>
                                        <p>数据尚未进行验证检查，是否确认直接导入？</p>
                                        <p class="text-muted small">
                                            <i class="fas fa-lightbulb"></i>
                                            建议：为确保数据质量，建议先进行数据验证检查后再导入。
                                        </p>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                        <i class="fas fa-times"></i> 取消
                                    </button>
                                    <button type="button" class="btn btn-warning" id="confirmImportBtn">
                                        <i class="fas fa-upload"></i> 确认导入
                                    </button>
                                    <button type="button" class="btn btn-primary" id="validateFirstBtn">
                                        <i class="fas fa-check-circle"></i> 先验证数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 添加到页面
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                const modal = new bootstrap.Modal(document.getElementById('importConfirmModal'));

                // 绑定事件
                document.getElementById('confirmImportBtn').onclick = () => {
                    modal.hide();
                    resolve(true);
                };

                document.getElementById('validateFirstBtn').onclick = () => {
                    modal.hide();
                    // 先进行数据验证
                    taskValidation.startValidation(task.id);
                    resolve(false);
                };

                // 模态框关闭时清理
                document.getElementById('importConfirmModal').addEventListener('hidden.bs.modal', () => {
                    document.getElementById('importConfirmModal').remove();
                    resolve(false);
                });

                modal.show();
            });
        }

        /**
         * 执行导入操作
         */
        async function executeImport(task, template) {
            let progressModal = null;

            try {
                console.log('开始执行导入操作:', { task, template });

                // 1. 显示导入进度对话框
                progressModal = showImportProgressDialog(task, template);

                // 2. 调用现有的导入接口
                const importResult = await callImportAPI(task, template);

                // 3. 更新任务状态为已导入
                await updateTaskStatusAfterImport(task.id, importResult);

                // 4. 显示成功消息
                hideImportProgressDialog(progressModal);
                showImportSuccessDialog(importResult);

                // 5. 刷新任务列表
                await refreshTaskList();

            } catch (error) {
                console.error('导入执行失败:', error);
                hideImportProgressDialog(progressModal);
                taskValidation.showError('导入失败: ' + error.message);
            }
        }

        /**
         * 调用统一的导入API
         */
        async function callImportAPI(task, template) {
            try {
                console.log('调用导入API - 任务和模板信息:', { task, template });

                // 验证必要参数
                if (!task.id) {
                    throw new Error('任务ID不能为空');
                }

                console.log('调用统一导入接口参数:', {
                    taskId: task.id,
                    taskName: task.taskName,
                    templateId: task.templateId,
                    templateName: template ? template.nameZh : '未知'
                });

                // 使用统一的执行导入接口
                const response = await fetch(`/api/file-import/tasks/${task.id}/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        // 可以传递额外的执行参数
                    })
                });

                const result = await response.json();

                if (result.success) {
                    console.log('导入API调用成功:', result);
                    return result;
                } else {
                    throw new Error(result.message || '导入API调用失败');
                }

            } catch (error) {
                console.error('导入API调用失败:', error);
                throw error;
            }
        }

        /**
         * 更新任务状态为已导入
         */
        async function updateTaskStatusAfterImport(taskId, importResult) {
            try {
                console.log('更新任务状态为已导入 - 任务ID:', taskId);

                // 使用统一的状态更新接口
                const response = await fetch(`/api/file-import/tasks/${taskId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `dataStatus=2` // DATA_IMPORTED = 2
                });

                const result = await response.json();

                if (!result.success) {
                    console.warn('更新任务状态失败:', result.message);
                    // 不抛出错误，因为导入已经成功
                }

            } catch (error) {
                console.error('更新任务状态失败:', error);
                // 不抛出错误，因为导入已经成功
            }
        }

        /**
         * 显示导入进度对话框
         */
        function showImportProgressDialog(task, template) {
            const modalHtml = `
                <div class="modal fade" id="importProgressModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-upload text-primary"></i>
                                    正在导入数据
                                </h5>
                            </div>
                            <div class="modal-body">
                                <div class="text-center">
                                    <div class="spinner-border text-primary mb-3" role="status">
                                        <span class="visually-hidden">导入中...</span>
                                    </div>
                                    <h6>正在导入数据到数据库...</h6>
                                    <p class="text-muted">
                                        任务：${task.taskName || '未知'}<br>
                                        模板：${template ? template.nameZh : '未知'}<br>
                                        记录数：${task.recordCount || 0} 条
                                    </p>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" style="width: 100%"></div>
                                    </div>
                                    <p class="text-muted mt-2 small">请稍候，正在处理数据...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('importProgressModal'));
            modal.show();

            return modal;
        }

        /**
         * 隐藏导入进度对话框
         */
        function hideImportProgressDialog(modal) {
            if (modal) {
                modal.hide();
                setTimeout(() => {
                    const modalElement = document.getElementById('importProgressModal');
                    if (modalElement) {
                        modalElement.remove();
                    }
                }, 300);
            }
        }

        /**
         * 显示导入成功对话框
         */
        function showImportSuccessDialog(importResult) {
            const summary = importResult.importSummary || {};

            const modalHtml = `
                <div class="modal fade" id="importSuccessModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-check-circle text-success"></i>
                                    数据导入成功
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-thumbs-up"></i> 导入完成</h6>
                                    <p>数据已成功导入到数据库中！</p>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-info-circle"></i> 导入摘要</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>模板名称：</strong>${summary.templateName || '未知'}</li>
                                            <li><strong>总要素数：</strong>${summary.totalFeatures || 0} 个</li>
                                            <li><strong>转换记录：</strong>${summary.convertedEntities || 0} 条</li>
                                            <li><strong>插入记录：</strong>${summary.insertedRecords || 0} 条</li>
                                            <li><strong>导入耗时：</strong>${summary.insertTime || '未知'}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-database"></i> 目标信息</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>目标表：</strong>${importResult.targetTable?.tableName || '默认表'}</li>
                                            <li><strong>字段映射：</strong>${Object.keys(importResult.fieldMapping || {}).length} 个字段</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-success" data-bs-dismiss="modal">
                                    <i class="fas fa-check"></i> 确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('importSuccessModal'));

            // 模态框关闭时清理
            document.getElementById('importSuccessModal').addEventListener('hidden.bs.modal', () => {
                document.getElementById('importSuccessModal').remove();
            });

            modal.show();

            // 显示成功提示
            taskValidation.showSuccess('数据导入成功！');
        }

        /**
         * 查看任务详情
         */
        async function viewTaskDetails(taskId) {
            try {
                // 验证taskId参数
                if (!taskId || taskId === 'undefined' || taskId === undefined) {
                    taskValidation.showError('任务ID无效');
                    return;
                }

                const numericTaskId = parseInt(taskId);
                if (isNaN(numericTaskId)) {
                    taskValidation.showError('任务ID格式错误');
                    return;
                }

                console.log('查看任务详情 - 任务ID:', numericTaskId);

                const response = await fetch(`/api/file-import/tasks/${numericTaskId}`);
                const result = await response.json();

                if (result.code === "200" || result.code === 200) {
                    // 显示任务详情模态框
                    taskValidation.showInfo('任务详情功能开发中...');
                } else {
                    taskValidation.showError('获取任务详情失败: ' + (result.msg || result.message));
                }

            } catch (error) {
                console.error('获取任务详情失败:', error);
                taskValidation.showError('获取任务详情失败: ' + error.message);
            }
        }

        /**
         * 查看导入结果
         */
        function viewImportResult(taskId) {
            // 验证taskId参数
            if (!taskId || taskId === 'undefined' || taskId === undefined) {
                taskValidation.showError('任务ID无效');
                return;
            }

            console.log('查看导入结果 - 任务ID:', taskId);
            taskValidation.showInfo('查看导入结果功能开发中...');
        }

        /**
         * 删除任务
         */
        async function deleteTask(taskId) {
            // 验证taskId参数
            if (!taskId || taskId === 'undefined' || taskId === undefined) {
                taskValidation.showError('任务ID无效');
                return;
            }

            const numericTaskId = parseInt(taskId);
            if (isNaN(numericTaskId)) {
                taskValidation.showError('任务ID格式错误');
                return;
            }

            if (!confirm('确定要删除这个任务吗？此操作不可撤销。')) {
                return;
            }

            try {
                console.log('删除任务 - 任务ID:', numericTaskId);

                const response = await fetch(`/api/file-import/tasks/${numericTaskId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                // 适配framework的Result格式
                if (result.code === "200" || result.code === 200) {
                    taskValidation.showSuccess('任务删除成功');
                    refreshTaskList();
                } else {
                    taskValidation.showError('删除任务失败: ' + (result.msg || result.message || '未知错误'));
                }

            } catch (error) {
                console.error('删除任务失败:', error);
                taskValidation.showError('删除任务失败: ' + error.message);
            }
        }

        /**
         * 显示错误消息
         */
        function showError(message) {
            taskValidation.showError(message);
        }
    </script>
</body>
</html>
