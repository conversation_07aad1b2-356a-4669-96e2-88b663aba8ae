package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GisManageTemplate;
import java.io.InputStream;

public interface ShapefileReaderService {

    /**
     * 从ZIP文件输入流读取Shapefile并保存到数据库
     *
     * @param zipInputStream ZIP文件输入流
     * @param fileName       原始文件名
     * @return 处理的要素数量
     */
    int processShapefileZipWithTemplate(InputStream zipInputStream, String fileName, GisManageTemplate template);

}
