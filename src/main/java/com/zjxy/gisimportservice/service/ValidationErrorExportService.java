package com.zjxy.gisimportservice.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.zjxy.gisimportservice.entity.ValidationErrorReport;
import com.zjxy.gisimportservice.listener.ExcelDataListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 验证错误Excel导出服务
 * 基于EasyExcel最佳实践的专业错误报告导出
 */
@Slf4j
@Service
public class ValidationErrorExportService {

    private static final String ERROR_FILE_DIR = "D:/xinyu_shixi/temp/validation-errors/";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    /**
     * 导出验证错误到Excel文件（新版本 - 基于最佳实践）
     *
     * @param detailedErrors 详细错误列表
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @return 生成的文件路径
     */
    public String exportValidationErrors(List<ExcelDataListener.DetailedValidationError> detailedErrors,
                                       Long taskId, String taskName) {
        try {
            // 1. 确保目录存在
            Path dirPath = Paths.get(ERROR_FILE_DIR);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
            }

            // 2. 生成文件名
            String timestamp = LocalDateTime.now().format(FORMATTER);
            String fileName = String.format("验证错误报告_%s_任务%d_%s.xlsx",
                    taskName != null ? taskName : "未命名", taskId, timestamp);
            String filePath = ERROR_FILE_DIR + fileName;

            // 3. 转换为专业的错误报告格式
            List<ValidationErrorReport> errorReports = convertToErrorReports(detailedErrors);

            // 4. 按错误级别和行号排序
            errorReports.sort(Comparator
                    .comparing(ValidationErrorReport::getSeverity)
                    .thenComparing(ValidationErrorReport::getRowNumber));

            // 5. 使用EasyExcel导出（简化版本，避免复杂样式问题）
            EasyExcel.write(filePath, ValidationErrorReport.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("错误详情")
                    .doWrite(errorReports);

            log.info("专业验证错误报告生成成功: {}, 错误数量: {}", filePath, detailedErrors.size());
            return filePath;

        } catch (IOException e) {
            log.error("生成验证错误Excel文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成验证错误文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 转换为专业的错误报告格式
     */
    private List<ValidationErrorReport> convertToErrorReports(List<ExcelDataListener.DetailedValidationError> detailedErrors) {
        List<ValidationErrorReport> reports = new ArrayList<>();

        for (int i = 0; i < detailedErrors.size(); i++) {
            ExcelDataListener.DetailedValidationError error = detailedErrors.get(i);

            // 分析错误类型和级别
            ValidationErrorReport.ErrorLevel level = determineErrorLevel(error.getErrorType());
            ValidationErrorReport.ErrorCategory category = determineErrorCategory(error.getErrorType());

            // 获取字段显示名称
            String fieldDisplayName = getFieldDisplayName(error.getFieldName());

            ValidationErrorReport report = ValidationErrorReport.createError(
                    i + 1,  // 错误序号
                    error.getRowNumber(),
                    error.getFieldName(),
                    fieldDisplayName,
                    error.getFieldValue(),
                    error.getErrorType(),
                    error.getErrorMessage(),
                    level,
                    category
            );

            // 设置原始数据
            if (error.getOriginalData() != null) {
                Map<String, Object> stringKeyMap = error.getOriginalData().entrySet().stream()
                        .collect(Collectors.toMap(
                                entry -> "列" + entry.getKey(),
                                Map.Entry::getValue
                        ));
                report.setOriginalRowDataFromMap(stringKeyMap);
            }

            reports.add(report);
        }

        return reports;
    }

    /**
     * 确定错误级别
     */
    private ValidationErrorReport.ErrorLevel determineErrorLevel(String errorType) {
        switch (errorType) {
            case "REQUIRED_FIELD":
            case "DATA_TYPE_MISMATCH":
            case "COORDINATE_ERROR":
            case "CONVERSION_FAILED":
                return ValidationErrorReport.ErrorLevel.CRITICAL;
            case "INVALID_FORMAT":
            case "OUT_OF_RANGE":
            case "CONVERSION_ERROR":
                return ValidationErrorReport.ErrorLevel.WARNING;
            default:
                return ValidationErrorReport.ErrorLevel.INFO;
        }
    }

    /**
     * 确定错误分类
     */
    private ValidationErrorReport.ErrorCategory determineErrorCategory(String errorType) {
        switch (errorType) {
            case "REQUIRED_FIELD":
                return ValidationErrorReport.ErrorCategory.REQUIRED_FIELD;
            case "DATA_TYPE_MISMATCH":
                return ValidationErrorReport.ErrorCategory.DATA_TYPE;
            case "INVALID_FORMAT":
                return ValidationErrorReport.ErrorCategory.DATA_FORMAT;
            case "OUT_OF_RANGE":
                return ValidationErrorReport.ErrorCategory.DATA_RANGE;
            case "COORDINATE_ERROR":
                return ValidationErrorReport.ErrorCategory.COORDINATE;
            case "CONVERSION_FAILED":
            case "CONVERSION_ERROR":
                return ValidationErrorReport.ErrorCategory.DATA_TYPE;
            default:
                return ValidationErrorReport.ErrorCategory.OTHER;
        }
    }

    /**
     * 获取字段显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        // 这里可以从模板配置或字典中获取字段的中文名称
        // 暂时返回字段名本身
        return fieldName != null ? fieldName : "未知字段";
    }



    /**
     * 兼容旧版本的导出方法
     */
    public String exportValidationErrors(List<ExcelDataListener.DetailedValidationError> detailedErrors, Long taskId) {
        return exportValidationErrors(detailedErrors, taskId, "数据验证");
    }
}
