package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GisManageTemplateValid;
import com.zjxy.gisimportservice.service.GisManageTemplateValidService;
import com.zjxy.gisimportservice.util.ValidationRuleConfigHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 验证规则初始化服务
 * 用于初始化和管理字段验证规则配置
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-29
 */
@Slf4j
@Service
public class ValidationRuleInitService implements ApplicationRunner {

    @Autowired
    private GisManageTemplateValidService templateValidService;

    /**
     * 应用完全启动后进行系统就绪检查
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("应用启动完成，进行系统就绪检查...");

        try {
            // 延迟一秒确保所有Bean都已初始化
            Thread.sleep(1000);

            // 检查字段验证服务是否可用
            if (templateValidService != null) {
                log.info("字段验证服务已就绪");
            } else {
                log.warn("字段验证服务未就绪");
            }

            // 检查验证规则配置助手是否可用
            try {
                ValidationRuleConfigHelper.getEnabledRuleCount(null);
                log.info("验证规则配置助手已就绪");
            } catch (Exception e) {
                log.warn("验证规则配置助手检查失败: {}", e.getMessage());
            }


        } catch (Exception e) {
            log.error("系统就绪检查失败", e);
        }
    }

}
