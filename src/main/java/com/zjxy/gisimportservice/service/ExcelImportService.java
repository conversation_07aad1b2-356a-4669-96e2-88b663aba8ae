package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.ExcelImportResult;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * Excel导入服务接口
 *
 * 提供Excel文件的导入、解析和验证功能，支持模板驱动的数据处理
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
public interface ExcelImportService {

    /**
     * Excel文件导入
     *
     * @param file Excel文件
     * @param templateId 模板ID
     * @param target 导入目标类型（import/valid/export）
     * @param createdBy 创建用户
     * @return 导入结果
     * @throws IOException 文件读取异常
     * @throws ParseException 数据解析异常
     */
    ExcelImportResult importExcelData(
        MultipartFile file,
        Integer templateId,
        String target,
        String createdBy
    ) throws IOException, ParseException;

    /**
     * Excel文件分析
     *
     * 分析Excel文件结构，返回列信息和数据预览
     *
     * @param file Excel文件
     * @param headerRow 表头行号（从1开始）
     * @return 分析结果，包含列信息和数据预览
     * @throws IOException 文件读取异常
     */
    Map<String, Object> analyzeExcelFile(
        MultipartFile file,
        Integer headerRow
    ) throws IOException;

    /**
     * 获取Excel工作表名称列表
     *
     * @param file Excel文件
     * @return 工作表名称列表
     * @throws IOException 文件读取异常
     */
    List<String> getSheetNames(MultipartFile file) throws IOException;

    /**
     * 分析Excel附件数据
     *
     * 根据模板配置分析Excel中的附件数据
     *
     * @param file Excel文件
     * @param templateId 模板ID
     * @return 附件数据分析结果
     * @throws IOException 文件读取异常
     */
    Map<String, List<Map<String, Object>>> analyzeExcelAttachment(
        MultipartFile file,
        Integer templateId
    ) throws IOException;

    /**
     * 验证Excel数据
     *
     * 根据模板配置验证Excel数据的完整性和正确性
     *
     * @param file Excel文件
     * @param templateId 模板ID
     * @return 验证结果
     * @throws IOException 文件读取异常
     */
    Map<String, Object> validateExcelData(
        MultipartFile file,
        Integer templateId
    ) throws IOException;

    /**
     * 批量导入Excel数据
     *
     * 支持大文件的分批处理导入
     *
     * @param file Excel文件
     * @param templateId 模板ID
     * @param batchSize 批次大小
     * @param target 导入目标类型
     * @param createdBy 创建用户
     * @return 导入结果
     * @throws IOException 文件读取异常
     * @throws ParseException 数据解析异常
     */
    ExcelImportResult batchImportExcelData(
        MultipartFile file,
        Integer templateId,
        Integer batchSize,
        String target,
        String createdBy
    ) throws IOException, ParseException;

    // ==================== 任务驱动模式新增方法 ====================

    /**
     * 保存上传的文件
     *
     * @param file 上传的文件
     * @param taskId 任务ID
     * @return 文件保存路径
     * @throws IOException 文件保存异常
     */
    String saveUploadedFile(MultipartFile file, String taskId) throws IOException;

    /**
     * 验证Excel数据（基于文件路径）
     *
     * @param filePath 文件路径
     * @param template 模板配置
     * @param target 目标类型（valid/import）
     * @param createdBy 创建用户
     * @param taskId 任务ID（用于生成错误文件）
     * @return 验证结果
     */
    ValidationResult validateExcelData(String filePath, GisManageTemplate template,
                                     String target, String createdBy, Long taskId);
}
