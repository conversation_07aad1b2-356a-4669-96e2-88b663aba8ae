package com.zjxy.gisimportservice.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjxy.gisimportservice.entity.GisManageTemplate;

import java.util.List;

/**
 * GIS管理模板服务接口
 */

public interface GisManageTemplateService extends IService<GisManageTemplate> {

    /**
     * 根据模板ID获取模板
     *
     * @param templateId 模板ID
     * @return 模板信息
     */
    GisManageTemplate getTemplateById(Integer templateId);

    /**
     * 根据表名获取模板列表
     *
     * @param tableName 表名
     * @return 模板列表
     */
    List<GisManageTemplate> getTemplatesByTableName(String tableName);

    /**
     * 获取所有导入模板
     *
     * @return 模板列表
     */
    List<GisManageTemplate> getAllImportTemplates();

    /**
     * 创建模板
     *
     * @param template 模板信息
     * @return 模板ID
     */
    Integer createTemplate(GisManageTemplate template);

    /**
     * 更新模板
     *
     * @param template 模板信息
     * @return 更新结果
     */
    Boolean updateTemplate(GisManageTemplate template);

    /**
     * 删除模板
     *
     * @param templateId 模板ID
     * @return 删除结果
     */
    Boolean deleteTemplate(Integer templateId);

    /**
     * 分页查询模板
     *
     * @param id 模板ID
     * @param tableName 表名
     * @param nameZh 中文名称
     * @param dataBase 数据库名
     * @param pageSize 分页大小
     * @param pageIndex 页码
     * @return 模板分页结果
     */
    Page<GisManageTemplate> getTemplatesPage(Integer id, String tableName, String nameZh, String dataBase, Long pageSize, Long pageIndex);



}
