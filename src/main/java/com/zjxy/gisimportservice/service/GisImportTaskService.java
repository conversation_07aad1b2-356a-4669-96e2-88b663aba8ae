package com.zjxy.gisimportservice.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.ValidationResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * GIS数据导入任务服务接口
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
public interface GisImportTaskService extends IService<GisImportTask> {

    /**
     * 创建导入任务
     *
     * @param taskName 任务名称
     * @param templateId 模板ID
     * @param importFormat 导入格式
     * @param filePath 文件路径
     * @param fileSize 文件大小
     * @param createdBy 创建用户
     * @return 创建的任务
     */
    GisImportTask createImportTask(String taskName, Integer templateId,
                                  GisImportTask.ImportFormat importFormat,
                                  String filePath, Long fileSize, String createdBy);



    /**
     * 根据模板ID查询导入任务列表
     *
     * @param templateId 模板ID
     * @return 导入任务列表
     */
    List<GisImportTask> getTasksByTemplateId(Integer templateId);

    /**
     * 根据数据状态查询导入任务列表
     *
     * @param dataStatus 数据状态
     * @return 导入任务列表
     */
    List<GisImportTask> getTasksByDataStatus(GisImportTask.DataStatus dataStatus);

    /**
     * 分页查询导入任务列表
     *
     * @param page 分页参数
     * @param dataStatus 数据状态（可选）
     * @param importFormat 导入格式（可选）
     * @return 分页结果
     */
    IPage<GisImportTask> pageQuery(Page<GisImportTask> page,
                                  GisImportTask.DataStatus dataStatus,
                                  GisImportTask.ImportFormat importFormat);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param dataStatus 数据状态
     * @return 是否更新成功
     */
    boolean updateTaskStatus(Long taskId, GisImportTask.DataStatus dataStatus);


    /**
     * 获取任务统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getTaskStatistics();

    /**
     * 删除任务（包括相关文件）
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteTask(Long taskId);

    /**
     * 获取任务详细信息（包括模板信息）
     *
     * @param taskId 任务ID
     * @return 任务详细信息
     */
    Map<String, Object> getTaskDetails(Long taskId);

    // ==================== Excel导入任务相关方法 ====================

    /**
     * 创建Excel导入任务
     *
     * @param task Excel导入任务信息
     * @param file Excel文件
     * @return 创建的任务
     */
    GisImportTask createExcelImportTask(GisImportTask task, MultipartFile file);

    /**
     * 获取任务列表
     *
     * @param createdBy 创建用户
     * @param status 状态
     * @param importFormat 导入格式
     * @param page 页码
     * @param size 页大小
     * @return 任务列表
     */
    List<GisImportTask> getTaskList(String createdBy, String status, GisImportTask.ImportFormat importFormat, Integer page, Integer size);

    /**
     * 验证Excel数据
     *
     * @param task 任务信息
     * @return 验证结果
     */
    ValidationResult validateExcelData(GisImportTask task);

    /**
     * 执行Excel数据导入
     *
     * @param task 任务信息
     * @return 导入结果
     */
    Map<String, Object> executeExcelImport(GisImportTask task);

    // ==================== Shapefile导入任务相关方法 ====================

    /**
     * 创建Shapefile导入任务
     *
     * @param task Shapefile导入任务信息
     * @param file Shapefile文件
     * @return 创建的任务
     */
    GisImportTask createShapefileImportTask(GisImportTask task, MultipartFile file);

    /**
     * 验证Shapefile数据
     *
     * @param task 任务信息
     * @return 验证结果
     */
    ValidationResult validateShapefileData(GisImportTask task);

    /**
     * 执行Shapefile数据导入
     *
     * @param task 任务信息
     * @return 导入结果
     */
    Map<String, Object> executeShapefileImport(GisImportTask task);

    /**
     * 获取任务的验证结果
     *
     * @param taskId 任务ID
     * @return 验证结果
     */
    ValidationResult getValidationResult(Long taskId);

}
