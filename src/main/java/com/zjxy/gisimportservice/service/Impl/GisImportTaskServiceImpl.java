package com.zjxy.gisimportservice.service.Impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.mapper.GisImportTaskMapper;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.ExcelImportService;
import com.zjxy.gisimportservice.service.GisImportTaskService;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import com.zjxy.gisimportservice.service.TemplateBasedShapefileService;
import org.springframework.web.multipart.MultipartFile;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.HashMap;
import java.util.Map;

/**
 * GIS数据导入任务服务实现类
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
@Slf4j
@Service
@DS("slave")
public class GisImportTaskServiceImpl extends ServiceImpl<GisImportTaskMapper, GisImportTask>
        implements GisImportTaskService {

    @Autowired
    private GisManageTemplateService templateService;

    @Autowired
    private ExcelImportService excelImportService;

    @Autowired
    private TemplateBasedShapefileService shapefileService;

    @Autowired
    private DataValidationService dataValidationService;


    @Override
    @Transactional
    public GisImportTask createImportTask(String taskName, Integer templateId,
                                         GisImportTask.ImportFormat importFormat,
                                         String filePath, Long fileSize, String createdBy) {
        try {
            // 验证模板是否存在
            GisManageTemplate template = templateService.getTemplateById(templateId);
            if (template == null) {
                throw new IllegalArgumentException("模板不存在，模板ID: " + templateId);
            }

            // 创建导入任务
            GisImportTask task = new GisImportTask();
            task.setTaskName(taskName);
            task.setTemplateId(templateId);
            task.setImportFormat(importFormat);
            task.setDataStatus(GisImportTask.DataStatus.DATA_UNCHECKED); // 修复：初始状态应该是数据未检查
            task.setFilePath(filePath);
            task.setFileSize(fileSize);
            task.setCreatedBy(createdBy);
            task.setImportTime(Timestamp.valueOf(LocalDateTime.now().withSecond(0).withNano(0)));
            task.setProcessedCount(0);
            task.setErrorCount(0);

            // 保存到数据库
            boolean saved = save(task);
            if (!saved) {
                throw new RuntimeException("保存导入任务失败");
            }

            log.info("创建导入任务成功 - 任务ID: {}, 任务名称: {}, 模板ID: {}, 文件路径: {}",
                    task.getId(), taskName, templateId, filePath);

            return task;

        } catch (Exception e) {
            log.error("创建导入任务失败 - 任务名称: {}, 模板ID: {}, 错误: {}",
                     taskName, templateId, e.getMessage(), e);
            throw new RuntimeException("创建导入任务失败: " + e.getMessage(), e);
        }
    }


    @Override
    public List<GisImportTask> getTasksByTemplateId(Integer templateId) {
        return baseMapper.findByTemplateId(templateId);
    }

    @Override
    public List<GisImportTask> getTasksByDataStatus(GisImportTask.DataStatus dataStatus) {
        return baseMapper.findByDataStatus(dataStatus.getCode());
    }

    @Override
    public IPage<GisImportTask> pageQuery(Page<GisImportTask> page,
                                         GisImportTask.DataStatus dataStatus,
                                         GisImportTask.ImportFormat importFormat) {
        Integer statusCode = dataStatus != null ? dataStatus.getCode() : null;
        Integer formatCode = importFormat != null ? importFormat.getCode() : null;
        return baseMapper.pageQuery(page, statusCode, formatCode);
    }

    @Override
    @Transactional
    public boolean updateTaskStatus(Long taskId, GisImportTask.DataStatus dataStatus) {
        try {
            int updated = baseMapper.updateTaskStatus(taskId, dataStatus.getCode());
            log.info("更新任务状态 - 任务ID: {}, 状态: {}, 结果: {}",
                    taskId, dataStatus.getDescription(), updated > 0 ? "成功" : "失败");
            return updated > 0;
        } catch (Exception e) {
            log.error("更新任务状态失败 - 任务ID: {}, 状态: {}, 错误: {}",
                     taskId, dataStatus.getDescription(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新任务为验证完成状态
     * data_status: 1 → 0
     */
    @Transactional
    public void updateToValidated(Long taskId, ValidationResult result) {
        // 重新从数据库获取最新状态，避免使用过期的对象状态
        GisImportTask task = this.getById(taskId);
        if (task == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }

        // 检查状态转换合法性 - 只允许从 DATA_UNCHECKED(1) 转换到 NOT_IMPORTED(0)
        if (task.getDataStatus() != GisImportTask.DataStatus.DATA_UNCHECKED) {
            log.warn("任务状态检查 - 任务ID: {}, 当前状态: {}, 允许验证操作继续",
                    taskId, task.getDataStatus().getDescription());
            // 不抛出异常，允许重新验证
        }

        // 更新状态
        log.info("验证成功，准备更新状态 - 任务ID: {}, 当前状态: {}, 目标状态: NOT_IMPORTED(0)",
                taskId, task.getDataStatus(), GisImportTask.DataStatus.NOT_IMPORTED);

        task.setDataStatus(GisImportTask.DataStatus.NOT_IMPORTED); // 数据未导入
        task.setValidationResult(JSON.toJSONString(result));

        log.info("状态设置完成 - 任务ID: {}, 设置后状态: {}, 状态码: {}",
                taskId, task.getDataStatus(), task.getDataStatus().getCode());

        // 直接使用SQL更新，避免可能的对象状态问题
        boolean updateResult = this.updateTaskStatus(taskId, GisImportTask.DataStatus.NOT_IMPORTED);
        log.info("任务状态更新完成: {} -> data_status=0(数据未导入), 更新结果: {}", taskId, updateResult);

        // 同时更新验证结果
        GisImportTask updateTask = new GisImportTask();
        updateTask.setId(taskId);
        updateTask.setValidationResult(JSON.toJSONString(result));
        this.updateById(updateTask);
    }

    /**
     * 更新任务为导入完成状态
     * data_status: 0 → 2
     */
    @Transactional
    public void updateToImported(Long taskId, ValidationResult result) {
        GisImportTask task = this.getById(taskId);
        if (task == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }

        // 检查状态转换合法性
        if (!task.canImport()) {
            throw new IllegalStateException("任务状态错误，当前状态: " + task.getDataStatus().getDescription() + "，无法执行导入");
        }

        // 更新状态
        task.setDataStatus(GisImportTask.DataStatus.DATA_IMPORTED); // 数据已导入
        task.setEndTime(new Timestamp(System.currentTimeMillis()));

        // 更新统计信息
        if (result != null) {
            task.setRecordCount(result.getTotalRecords());
            task.setProcessedCount(result.getValidRecords());
            task.setErrorCount(result.getErrorRecords());
        }

        this.updateById(task);
        log.info("任务状态更新: {} -> data_status=2(数据已导入)", taskId);
    }

    /**
     * 更新任务为验证失败状态
     * 保持data_status=1，但记录失败信息
     */
    @Transactional
    public void updateToValidationFailed(Long taskId, ValidationResult result) {
        GisImportTask task = this.getById(taskId);
        if (task == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }
        task.setValidationResult(JSON.toJSONString(result));
        task.setErrorMessage("验证失败: " + result.getSummary());


        this.updateById(task);
        log.warn("任务验证失败 - 任务ID: {}, 错误: {}", taskId, result.getSummary());
    }

    /**
     * 更新任务为导入失败状态
     * 保持data_status=0，但记录失败信息
     */
    @Transactional
    public void updateToImportFailed(Long taskId, String errorMessage) {
        GisImportTask task = this.getById(taskId);
        if (task == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }

        // 导入失败，保持data_status=0，但记录失败信息
        task.setErrorMessage("导入失败: " + errorMessage);
        task.setEndTime(new Timestamp(System.currentTimeMillis()));

        this.updateById(task);
        log.warn("任务导入失败 - 任务ID: {}, 错误: {}", taskId, errorMessage);
    }

    /**
     * 根据文件路径查找任务
     */
    public GisImportTask findByFilePath(String filePath) {
        QueryWrapper<GisImportTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_path", filePath);
        queryWrapper.orderByDesc("import_time");
        queryWrapper.last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public Map<String, Object> getTaskStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 总任务数
            long totalTasks = count();
            stats.put("totalTasks", totalTasks);

            // 各状态任务数
            for (GisImportTask.DataStatus status : GisImportTask.DataStatus.values()) {
                QueryWrapper<GisImportTask> wrapper = new QueryWrapper<>();
                wrapper.eq("data_status", status.getCode());
                long count = count(wrapper);
                stats.put(status.name().toLowerCase() + "Count", count);
            }

            // 各格式任务数
            for (GisImportTask.ImportFormat format : GisImportTask.ImportFormat.values()) {
                QueryWrapper<GisImportTask> wrapper = new QueryWrapper<>();
                wrapper.eq("import_format", format.getCode());
                long count = count(wrapper);
                stats.put(format.name().toLowerCase() + "Count", count);
            }

            // 未完成任务数
            int pendingTasks = baseMapper.countPendingTasks();
            stats.put("pendingTasks", pendingTasks);

        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            stats.put("error", "获取统计信息失败: " + e.getMessage());
        }

        return stats;
    }

    @Override
    @Transactional
    public boolean deleteTask(Long taskId) {
        try {
            // 获取任务信息
            GisImportTask task = getById(taskId);
            if (task == null) {
                log.warn("删除任务失败 - 任务不存在，任务ID: {}", taskId);
                return false;
            }

            // 删除相关文件
            if (task.getFilePath() != null) {
                try {
                    File file = new File(task.getFilePath());
                    if (file.exists()) {
                        file.delete();
                        log.info("删除任务文件: {}", task.getFilePath());
                    }
                } catch (Exception e) {
                    log.warn("删除任务文件失败: {}, 错误: {}", task.getFilePath(), e.getMessage());
                }
            }

            // 删除数据库记录
            boolean deleted = removeById(taskId);
            log.info("删除任务 - 任务ID: {}, 结果: {}", taskId, deleted ? "成功" : "失败");
            return deleted;

        } catch (Exception e) {
            log.error("删除任务失败 - 任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getTaskDetails(Long taskId) {
        Map<String, Object> details = new HashMap<>();

        try {
            GisImportTask task = getById(taskId);
            if (task == null) {
                details.put("error", "任务不存在");
                return details;
            }

            details.put("task", task);

            // 获取模板信息
            if (task.getTemplateId() != null) {
                GisManageTemplate template = templateService.getTemplateById(task.getTemplateId());
                details.put("template", template);
            }

            // 计算额外信息
            details.put("progressPercentage", task.getProgressPercentage());
            details.put("isCompleted", task.isCompleted());
            details.put("hasErrors", task.hasErrors());

        } catch (Exception e) {
            log.error("获取任务详细信息失败 - 任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            details.put("error", "获取任务详细信息失败: " + e.getMessage());
        }
        return details;
    }

    // ==================== Excel导入任务相关方法实现 ====================

    @Override
    @Transactional
    public GisImportTask createExcelImportTask(GisImportTask task, MultipartFile file) {
        try {
            log.info("创建Excel导入任务 - 任务名: {}, 模板ID: {}", task.getTaskName(), task.getTemplateId());

            // 1. 验证模板是否存在
            GisManageTemplate template = templateService.getTemplateById(task.getTemplateId());
            if (template == null) {
                throw new RuntimeException("模板不存在，ID: " + task.getTemplateId());
            }

            // 2. 设置任务基础信息
            task.setImportTime(new java.sql.Timestamp(System.currentTimeMillis()));
            task.setImportFormat(GisImportTask.ImportFormat.EXCEL);
            task.setDataStatus(GisImportTask.DataStatus.DATA_UNCHECKED); // 修复：初始状态应该是数据未检查
            task.setFileSize(file.getSize());

            // 3. 先保存任务到数据库以获取ID
            this.save(task);

            // 4. 使用生成的ID保存文件
            String filePath = excelImportService.saveUploadedFile(file, task.getId().toString());
            task.setFilePath(filePath);

            // 5. 更新任务的文件路径
            this.updateById(task);

            log.info("Excel导入任务创建成功 - 任务ID: {}, 文件路径: {}", task.getId(), filePath);
            return task;

        } catch (Exception e) {
            log.error("创建Excel导入任务失败", e);
            throw new RuntimeException("创建任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<GisImportTask> getTaskList(String createdBy, String status, GisImportTask.ImportFormat importFormat, Integer page, Integer size) {
        try {
            QueryWrapper<GisImportTask> queryWrapper = new QueryWrapper<>();

            if (createdBy != null && !createdBy.trim().isEmpty()) {
                queryWrapper.eq("created_by", createdBy);
            }
            if (importFormat != null) {
                queryWrapper.eq("import_format", importFormat);
            }

            queryWrapper.orderByDesc("updated_time");

            // PostgreSQL兼容的分页处理
            if (page != null && size != null && page > 0 && size > 0) {
                int offset = (page - 1) * size;
                // 使用PostgreSQL语法：LIMIT size OFFSET offset
                queryWrapper.last("LIMIT " + size + " OFFSET " + offset);
            }

            return this.list(queryWrapper);
        } catch (Exception e) {
            log.error("查询任务列表失败", e);
            throw new RuntimeException("查询任务列表失败: " + e.getMessage(), e);
        }
    }



    @Override
    public ValidationResult validateExcelData(GisImportTask task) {
        try {
            log.info("开始验证Excel数据 - 任务ID: {}, 当前状态: {}", task.getId(), task.getDataStatus().getDescription());

            // 检查任务状态是否允许验证
            if (!task.canValidate()) {
                throw new IllegalStateException("任务状态不允许执行验证操作，当前状态: " + task.getDataStatus().getDescription());
            }

            // 获取模板配置
            GisManageTemplate template = templateService.getTemplateById(task.getTemplateId());
            if (template == null) {
                throw new RuntimeException("模板不存在，ID: " + task.getTemplateId());
            }

            // 执行Excel数据验证（不导入，只验证）
            ValidationResult result = excelImportService.validateExcelData(task.getFilePath(), template, "valid", task.getCreatedBy(), task.getId());

            // 根据验证结果更新任务状态（统一状态管理）
            if (result.isPassed()) {
                this.updateToValidated(task.getId(), result);
                log.info("Excel验证成功，任务状态更新为: data_status=0(数据未导入) - 任务ID: {}", task.getId());
            } else {
                this.updateToValidationFailed(task.getId(), result);
                log.warn("Excel验证失败，任务状态保持为: data_status=1(数据未检查) - 任务ID: {}", task.getId());
            }

            return result;

        } catch (Exception e) {
            log.error("验证Excel数据失败 - 任务ID: {}", task.getId(), e);
            throw new RuntimeException("验证失败: " + e.getMessage(), e);
        }
    }

    @Override
    // 移除事务注解，避免与动态数据源切换冲突
    // @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeExcelImport(GisImportTask task) {
        log.info("开始执行Excel数据导入 - 任务ID: {}, 当前状态: {}", task.getId(), task.getDataStatus().getDescription());

        // 检查任务状态是否允许导入
        if (!task.canImport()) {
            throw new IllegalStateException("任务状态不允许执行导入操作，当前状态: " + task.getDataStatus().getDescription());
        }

        // 获取模板配置
        GisManageTemplate template = templateService.getTemplateById(task.getTemplateId());
        if (template == null) {
            throw new RuntimeException("模板不存在，ID: " + task.getTemplateId());
        }

        ValidationResult importResult = null;
        Map<String, Object> result = new HashMap<>();

        try {
            // 步骤1：执行实际的数据库插入操作（不更新任务状态）
            log.info("步骤1：开始执行Excel数据插入 - 任务ID: {}", task.getId());
            importResult = excelImportService.validateExcelData(task.getFilePath(), template, "import", task.getCreatedBy(), task.getId());

            // 步骤2：验证插入结果
            if (!importResult.isPassed()) {
                log.warn("Excel数据插入失败 - 任务ID: {}, 错误: {}", task.getId(), importResult.getSummary());
                throw new RuntimeException("数据插入失败: " + importResult.getSummary());
            }

            log.info("步骤2：Excel数据插入成功 - 任务ID: {}, 插入记录数: {}", task.getId(), importResult.getValidRecords());

            // 步骤3：数据插入成功后，更新任务状态
            log.info("步骤3：更新任务状态为已导入 - 任务ID: {}", task.getId());
            this.updateToImported(task.getId(), importResult);
            log.info("Excel导入完成，任务状态更新为: data_status=2(数据已导入) - 任务ID: {}", task.getId());

            // 构建成功返回结果
            result.put("success", true);
            result.put("totalRecords", importResult.getTotalRecords());
            result.put("validRecords", importResult.getValidRecords());
            result.put("errorRecords", importResult.getErrorRecords());
            result.put("importedRecords", importResult.getValidRecords());
            result.put("errors", importResult.getErrors());
            result.put("message", "Excel导入成功，共导入 " + importResult.getValidRecords() + " 条记录");

        } catch (Exception e) {
            log.error("Excel数据导入过程失败 - 任务ID: {}", task.getId(), e);

            // 步骤4：导入失败时，更新任务状态为导入失败（状态保持为0）
            String errorMessage = "Excel导入失败: " + e.getMessage();
            this.updateToImportFailed(task.getId(), errorMessage);
            log.warn("Excel导入失败，任务状态保持为: data_status=0(数据未导入) - 任务ID: {}", task.getId());

            // 构建失败返回结果
            result.put("success", false);
            result.put("totalRecords", importResult != null ? importResult.getTotalRecords() : 0);
            result.put("validRecords", 0);
            result.put("errorRecords", importResult != null ? importResult.getErrorRecords() : 1);
            result.put("importedRecords", 0);
            result.put("errors", importResult != null ? importResult.getErrors() : new ArrayList<>());
            result.put("message", errorMessage);

            // 重新抛出异常以触发事务回滚
            throw new RuntimeException(errorMessage, e);
        }

        return result;
    }

    // ==================== Shapefile导入任务相关方法实现 ====================

    @Override
    @Transactional
    public GisImportTask createShapefileImportTask(GisImportTask task, MultipartFile file) {
        try {
            log.info("创建Shapefile导入任务 - 任务名: {}, 模板ID: {}", task.getTaskName(), task.getTemplateId());

            // 1. 验证模板是否存在
            GisManageTemplate template = templateService.getTemplateById(task.getTemplateId());
            if (template == null) {
                throw new RuntimeException("模板不存在，ID: " + task.getTemplateId());
            }

            // 2. 设置任务基础信息
            task.setImportTime(new java.sql.Timestamp(System.currentTimeMillis()));
            task.setImportFormat(GisImportTask.ImportFormat.SHP);
            task.setDataStatus(GisImportTask.DataStatus.DATA_UNCHECKED); // 修复：初始状态应该是数据未检查
            task.setFileSize(file.getSize());

            // 3. 先保存任务到数据库以获取ID
            this.save(task);

            // 4. 使用生成的ID保存文件
            String filePath = shapefileService.saveUploadedFile(file, task.getId().toString());
            task.setFilePath(filePath);

            // 5. 更新任务的文件路径
            this.updateById(task);

            log.info("Shapefile导入任务创建成功 - 任务ID: {}, 文件路径: {}", task.getId(), filePath);
            return task;

        } catch (Exception e) {
            log.error("创建Shapefile导入任务失败", e);
            throw new RuntimeException("创建任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValidationResult validateShapefileData(GisImportTask task) {
        try {
            log.info("开始验证Shapefile数据 - 任务ID: {}, 当前状态: {}", task.getId(), task.getDataStatus().getDescription());

            // 检查任务状态是否允许验证
            if (!task.canValidate()) {
                throw new IllegalStateException("任务状态不允许执行验证操作，当前状态: " + task.getDataStatus().getDescription());
            }

            // 获取模板配置
            GisManageTemplate template = templateService.getTemplateById(task.getTemplateId());
            if (template == null) {
                throw new RuntimeException("模板不存在，ID: " + task.getTemplateId());
            }

            // 执行Shapefile数据验证（不导入，只验证）
            ValidationResult result = shapefileService.validateShapefileData(task.getFilePath(), template, "valid");

            // 根据验证结果更新任务状态（统一状态管理）
            if (result.isPassed()) {
                this.updateToValidated(task.getId(), result);
                log.info("Shapefile验证成功，任务状态更新为: data_status=0(数据未导入) - 任务ID: {}", task.getId());
            } else {
                this.updateToValidationFailed(task.getId(), result);
                log.warn("Shapefile验证失败，任务状态保持为: data_status=1(数据未检查) - 任务ID: {}", task.getId());
            }

            return result;

        } catch (Exception e) {
            log.error("验证Shapefile数据失败 - 任务ID: {}", task.getId(), e);
            throw new RuntimeException("验证失败: " + e.getMessage(), e);
        }
    }

    @Override
    // 移除事务注解，避免与动态数据源切换冲突
    // @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeShapefileImport(GisImportTask task) {
        log.info("开始执行Shapefile数据导入 - 任务ID: {}, 当前状态: {}", task.getId(), task.getDataStatus().getDescription());

        // 检查任务状态是否允许导入
        if (!task.canImport()) {
            throw new IllegalStateException("任务状态不允许执行导入操作，当前状态: " + task.getDataStatus().getDescription());
        }

        // 获取模板配置
        GisManageTemplate template = templateService.getTemplateById(task.getTemplateId());
        if (template == null) {
            throw new RuntimeException("模板不存在，ID: " + task.getTemplateId());
        }

        ValidationResult importResult = null;
        Map<String, Object> result = new HashMap<>();

        try {
            // 步骤1：执行实际的数据库插入操作（不更新任务状态）
            log.info("步骤1：开始执行Shapefile数据插入 - 任务ID: {}", task.getId());
            importResult = shapefileService.validateShapefileData(task.getFilePath(), template, "import");

            // 步骤2：验证插入结果
            if (!importResult.isPassed()) {
                log.warn("Shapefile数据插入失败 - 任务ID: {}, 错误: {}", task.getId(), importResult.getSummary());
                throw new RuntimeException("数据插入失败: " + importResult.getSummary());
            }

            log.info("步骤2：Shapefile数据插入成功 - 任务ID: {}, 插入记录数: {}", task.getId(), importResult.getValidRecords());

            // 步骤3：数据插入成功后，更新任务状态
            log.info("步骤3：更新任务状态为已导入 - 任务ID: {}", task.getId());
            this.updateToImported(task.getId(), importResult);
            log.info("Shapefile导入完成，任务状态更新为: data_status=2(数据已导入) - 任务ID: {}", task.getId());

            // 构建成功返回结果
            result.put("success", true);
            result.put("totalRecords", importResult.getTotalRecords());
            result.put("validRecords", importResult.getValidRecords());
            result.put("errorRecords", importResult.getErrorRecords());
            result.put("importedRecords", importResult.getValidRecords());
            result.put("errors", importResult.getErrors());
            result.put("message", "Shapefile导入成功，共导入 " + importResult.getValidRecords() + " 条记录");

        } catch (Exception e) {
            log.error("Shapefile数据导入过程失败 - 任务ID: {}", task.getId(), e);

            // 步骤4：导入失败时，更新任务状态为导入失败（状态保持为0）
            String errorMessage = "Shapefile导入失败: " + e.getMessage();
            this.updateToImportFailed(task.getId(), errorMessage);
            log.warn("Shapefile导入失败，任务状态保持为: data_status=0(数据未导入) - 任务ID: {}", task.getId());

            // 构建失败返回结果
            result.put("success", false);
            result.put("totalRecords", importResult != null ? importResult.getTotalRecords() : 0);
            result.put("validRecords", 0);
            result.put("errorRecords", importResult != null ? importResult.getErrorRecords() : 1);
            result.put("importedRecords", 0);
            result.put("errors", importResult != null ? importResult.getErrors() : new ArrayList<>());
            result.put("message", errorMessage);

            // 重新抛出异常以触发事务回滚
            throw new RuntimeException(errorMessage, e);
        }

        return result;
    }

    @Override
    public ValidationResult getValidationResult(Long taskId) {
        try {
            log.info("获取任务验证结果 - 任务ID: {}", taskId);

            // 1. 获取任务信息
            GisImportTask task = this.getById(taskId);
            if (task == null) {
                log.warn("任务不存在 - 任务ID: {}", taskId);
                return null;
            }

            // 2. 根据文件格式获取验证结果（传递任务对象避免重复查询）
            ValidationResult validationResult;
            if (task.getImportFormat() == GisImportTask.ImportFormat.EXCEL) {
                // 对于Excel文件，从验证服务获取结果
                validationResult = dataValidationService.getValidationResultByTask(task);
            } else if (task.getImportFormat() == GisImportTask.ImportFormat.SHP) {
                // 对于Shapefile，从验证服务获取结果
                validationResult = dataValidationService.getValidationResult(taskId);
            } else {
                log.warn("不支持的文件格式 - 任务ID: {}, 格式: {}", taskId, task.getImportFormat());
                return null;
            }

            return validationResult;

        } catch (Exception e) {
            log.error("获取任务验证结果失败 - 任务ID: {}", taskId, e);
            return null;
        }
    }

}
