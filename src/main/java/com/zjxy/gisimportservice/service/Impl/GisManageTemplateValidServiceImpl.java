package com.zjxy.gisimportservice.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.entity.GisManageTemplateValid;
import com.zjxy.gisimportservice.mapper.GisManageTemplateValidMapper;
import com.zjxy.gisimportservice.service.GisManageTemplateValidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 字段验证服务实现类
 *
 * <AUTHOR> Data Import System
 * @since 2025-07-29
 */
@Slf4j
@Service
public class GisManageTemplateValidServiceImpl extends ServiceImpl<GisManageTemplateValidMapper, GisManageTemplateValid>
        implements GisManageTemplateValidService {

    @Override
    @Transactional
    public Integer addValid(GisManageTemplateValid valid) {
        DynamicDataSourceManager.build().useDataSource("master");
        log.info("添加字段验证规则 - 模板ID: {}, UID: {}", valid.getTemplateId(), valid.getUid());

        boolean success = this.save(valid);
        if (success) {
            log.info("字段验证规则添加成功 - ID: {}", valid.getId());
            return valid.getId();
        } else {
            log.error("字段验证规则添加失败");
            throw new RuntimeException("添加字段验证规则失败");
        }
    }

    @Override
    @Transactional
    public void addValidBatch(List<GisManageTemplateValid> validList) {
        DynamicDataSourceManager.build().useDataSource("master");
        log.info("批量添加字段验证规则 - 数量: {}", validList.size());

        boolean success = this.saveBatch(validList);
        if (success) {
            log.info("批量添加字段验证规则成功");
        } else {
            log.error("批量添加字段验证规则失败");
            throw new RuntimeException("批量添加字段验证规则失败");
        }
    }

    @Override
    @Transactional
    public Boolean updateValid(GisManageTemplateValid valid) {
        DynamicDataSourceManager.build().useDataSource("master");
        log.info("更新字段验证规则 - ID: {}", valid.getId());

        boolean success = this.updateById(valid);
        if (success) {
            log.info("字段验证规则更新成功");
        } else {
            log.warn("字段验证规则更新失败 - ID: {}", valid.getId());
        }
        return success;
    }

    @Override
    @Transactional
    public Boolean updateValidBatch(List<GisManageTemplateValid> validList) {
        DynamicDataSourceManager.build().useDataSource("master");
        log.info("批量更新字段验证规则 - 数量: {}", validList.size());

        boolean success = this.updateBatchById(validList);
        if (success) {
            log.info("批量更新字段验证规则成功");
        } else {
            log.warn("批量更新字段验证规则失败");
        }
        return success;
    }

    @Override
    public List<GisManageTemplateValid> getValid(String uid, String templateId) {
        DynamicDataSourceManager.build().useDataSource("slave");
        log.info("查询字段验证规则 - UID: {}, 模板ID: {}", uid, templateId);

        QueryWrapper<GisManageTemplateValid> queryWrapper = new QueryWrapper<>();

        if (uid != null && !uid.trim().isEmpty()) {
            queryWrapper.eq("uid", uid);
        }

        if (templateId != null && !templateId.trim().isEmpty()) {
            queryWrapper.eq("template_id", templateId);
        }

        List<GisManageTemplateValid> result = this.list(queryWrapper);
        log.info("查询到字段验证规则数量: {}", result.size());

        return result;
    }

    @Override
    @Transactional
    public Boolean delValid(Integer id) {
        DynamicDataSourceManager.build().useDataSource("master");
        log.info("删除字段验证规则 - ID: {}", id);

        boolean success = this.removeById(id);
        if (success) {
            log.info("字段验证规则删除成功");
        } else {
            log.warn("字段验证规则删除失败 - ID: {}", id);
        }
        return success;
    }

    @Override
    public List<GisManageTemplateValid> getValidByUid(String uid) {
        DynamicDataSourceManager.build().useDataSource("slave");
        log.info("通过UID查询字段验证规则 - UID: {}", uid);

        QueryWrapper<GisManageTemplateValid> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);

        List<GisManageTemplateValid> result = this.list(queryWrapper);
        log.info("通过UID查询到字段验证规则数量: {}", result.size());

        return result;
    }

    @Override
    public GisManageTemplateValid getValidConfigByUid(String uid) {
        DynamicDataSourceManager.build().useDataSource("slave");
        log.info("根据UID查询验证配置 - UID: {}", uid);

        GisManageTemplateValid result = this.baseMapper.getByUid(uid);
        if (result != null) {
            log.info("找到验证配置 - ID: {}, UID: {}", result.getId(), uid);
            log.debug("验证配置详情 - sx字段: {}, tp字段: {}, kj字段: {}, map字段: {}",
                result.getSx(), result.getTp(), result.getKj(), result.getMap());
        } else {
            log.info("未找到UID为 {} 的验证配置", uid);
        }

        return result;
    }

    @Override
    public GisManageTemplateValid getValidByTemplateId(Integer templateId) {
        DynamicDataSourceManager.build().useDataSource("slave");
        log.info("根据模板ID查询验证配置 - 模板ID: {}", templateId);

        GisManageTemplateValid result = this.baseMapper.getByTemplateId(templateId);
        if (result != null) {
            log.info("找到验证配置 - ID: {}, 模板ID: {}", result.getId(), templateId);
        } else {
            log.info("未找到模板ID为 {} 的验证配置", templateId);
        }

        return result;
    }
}
