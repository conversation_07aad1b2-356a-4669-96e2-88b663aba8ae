package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * 基于模板的Shapefile处理服务接口
 */
public interface TemplateBasedShapefileService {

    /**
     * 使用模板处理Shapefile ZIP文件
     * @param zipInputStream ZIP文件输入流
     * @param fileName 文件名
     * @param templateId 模板ID
     * @return 处理结果
     */
    Map<String, Object> processShapefileWithTemplate(InputStream zipInputStream, String fileName, Integer templateId);

    /**
     * 使用模板处理Shapefile ZIP文件（从路径）
     * @param zipFilePath ZIP文件路径
     * @param templateId 模板ID
     * @return 处理结果
     */
    Map<String, Object> processShapefileWithTemplateFromPath(String zipFilePath, Integer templateId);


    /**
     * 根据模板生成处理报告
     * @param processedCount 处理的记录数
     * @param errorCount 错误记录数
     * @param template 模板配置
     * @return 处理报告
     */
    Map<String, Object> generateProcessingReport(int processedCount, int errorCount, GisManageTemplate template);

    // ==================== 任务驱动模式新增方法 ====================

    /**
     * 保存上传的Shapefile文件
     *
     * @param file 上传的文件
     * @param taskId 任务ID
     * @return 文件保存路径
     * @throws IOException 文件保存异常
     */
    String saveUploadedFile(MultipartFile file, String taskId) throws IOException;

    /**
     * 验证Shapefile数据（基于文件路径）
     *
     * @param filePath 文件路径
     * @param template 模板配置
     * @param target 目标类型（valid/import）
     * @return 验证结果
     */
    ValidationResult validateShapefileData(String filePath, GisManageTemplate template,
                                         String target);
}
