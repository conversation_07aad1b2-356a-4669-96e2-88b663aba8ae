package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.GisManageTemplateValid;
import com.zjxy.gisimportservice.entity.ValidationResult;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 数据验证服务接口
 * 提供完整的数据检查和验证功能
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
public interface DataValidationService {

    /**
     * 异步验证导入任务的数据
     *
     * @param taskId 任务ID
     * @return 异步验证结果
     */
    CompletableFuture<ValidationResult> validateTaskDataAsync(Long taskId);

    /**
     * 同步验证导入任务的数据
     *
     * @param taskId 任务ID
     * @return 验证结果
     */
    ValidationResult validateTaskData(Long taskId);

    /**
     * 验证Shapefile数据
     *
     * @param filePath 文件路径
     * @param template 模板配置
     * @param task 导入任务
     * @return 验证结果
     */
    ValidationResult validateShapefileData(String filePath, GisManageTemplate template, GisImportTask task);

    /**
     * 生成错误报告Excel文件
     *
     * @param validationResult 验证结果
     * @param outputPath 输出文件路径
     * @return 生成的文件路径
     */
    String generateErrorReport(ValidationResult validationResult, String outputPath);

    /**
     * 获取验证进度
     *
     * @param taskId 任务ID
     * @return 验证进度信息
     */
    Map<String, Object> getValidationProgress(Long taskId);

    /**
     * 取消验证任务
     *
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelValidation(Long taskId);

    /**
     * 获取验证配置
     *
     * @return 验证配置
     */
    ValidationResult.ValidationConfig getValidationConfig();



    /**
     * 检查任务是否可以进行验证
     *
     * @param taskId 任务ID
     * @return 检查结果
     */
    Map<String, Object> checkValidationEligibility(Long taskId);

    /**
     * 获取任务的验证结果
     *
     * @param taskId 任务ID
     * @return 验证结果
     */
    ValidationResult getValidationResult(Long taskId);

    /**
     * 获取任务的验证结果（基于任务对象，避免重复查询）
     *
     * @param task 任务对象
     * @return 验证结果
     */
    ValidationResult getValidationResultByTask(GisImportTask task);

    /**
     * 统一的模板验证方法（与文件格式无关）
     * 基于GisManageTemplateValid的sx属性进行验证
     *
     * @param data 数据对象（可以是GeoFeatureEntity或其他数据结构）
     * @param template 模板配置
     * @param rowIndex 行号
     * @param detailedErrors 详细错误收集器
     * @return 验证是否通过
     */
    boolean validateDataWithTemplate(Object data, GisManageTemplate template, int rowIndex,
                                   java.util.List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors);

    /**
     * 获取模板的验证配置
     *
     * @param templateId 模板ID
     * @return 验证配置
     */
    GisManageTemplateValid getValidationConfig(Integer templateId);

    /**
     * 统一的字段验证方法
     *
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param fieldConfig 字段配置
     * @param rowIndex 行号
     * @param detailedErrors 详细错误收集器
     * @return 验证是否通过
     */
    boolean validateField(String fieldName, Object fieldValue, java.util.Map<String, Object> fieldConfig,
                         int rowIndex, java.util.List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors);

    /**
     * 收集详细错误信息
     *
     * @param rowNumber 行号
     * @param errorMessage 错误信息
     * @param errorType 错误类型
     * @param originalData 原始数据
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param detailedErrors 详细错误收集器
     */
    void collectDetailedError(int rowNumber, String errorMessage, String errorType,
                            java.util.Map<Integer, Object> originalData, String fieldName, Object fieldValue,
                            java.util.List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors);

    /**
     * 生成专业错误报告
     *
     * @param detailedErrors 详细错误列表
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @return 错误报告文件路径
     */
    String generateErrorReport(java.util.List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors,
                              Long taskId, String taskName);

}
