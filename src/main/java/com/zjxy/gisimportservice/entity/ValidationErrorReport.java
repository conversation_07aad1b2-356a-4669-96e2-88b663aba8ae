package com.zjxy.gisimportservice.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 验证错误报告实体（简化版本）
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationErrorReport {

    /**
     * 错误序号
     */
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(8)
    private Integer errorIndex;

    /**
     * 错误级别
     */
    @ExcelProperty(value = "错误级别", index = 1)
    @ColumnWidth(12)
    private String errorLevel;

    /**
     * 数据行号
     */
    @ExcelProperty(value = "数据行号", index = 2)
    @ColumnWidth(12)
    private Integer rowNumber;

    /**
     * 错误字段
     */
    @ExcelProperty(value = "错误字段", index = 3)
    @ColumnWidth(15)
    private String fieldName;

    /**
     * 字段中文名
     */
    @ExcelProperty(value = "字段说明", index = 4)
    @ColumnWidth(15)
    private String fieldDisplayName;

    /**
     * 错误值
     */
    @ExcelProperty(value = "错误值", index = 5)
    @ColumnWidth(20)
    private String errorValue;

    /**
     * 错误类型
     */
    @ExcelProperty(value = "错误类型", index = 6)
    @ColumnWidth(15)
    private String errorType;

    /**
     * 错误描述
     */
    @ExcelProperty(value = "错误描述", index = 7)
    @ColumnWidth(30)
    private String errorMessage;

    /**
     * 建议修正
     */
    @ExcelProperty(value = "建议修正", index = 8)
    @ColumnWidth(25)
    private String suggestion;

    /**
     * 原始数据行
     */
    @ExcelProperty(value = "原始数据", index = 9)
    @ColumnWidth(40)
    private String originalRowData;

    /**
     * 检测时间
     */
    @ExcelProperty(value = "检测时间", index = 10)
    @ColumnWidth(20)
    private String detectionTime;

    // ========== 内部使用字段（不导出到Excel）==========

    /**
     * 错误分类（用于统计）
     */
    @ExcelIgnore
    private ErrorCategory category;

    /**
     * 错误严重程度（用于排序）
     */
    @ExcelIgnore
    private Integer severity;

    /**
     * 关联的原始数据Map
     */
    @ExcelIgnore
    private java.util.Map<String, Object> rawData;

    /**
     * 错误分类枚举
     */
    public enum ErrorCategory {
        REQUIRED_FIELD("必填字段"),
        DATA_TYPE("数据类型"),
        DATA_FORMAT("数据格式"),
        DATA_RANGE("数据范围"),
        BUSINESS_RULE("业务规则"),
        COORDINATE("坐标数据"),
        REFERENCE("引用完整性"),
        OTHER("其他错误");

        private final String description;

        ErrorCategory(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 错误级别枚举
     */
    public enum ErrorLevel {
        CRITICAL("严重", 1, "数据无法导入"),
        WARNING("警告", 2, "建议修正"),
        INFO("提示", 3, "可选修正");

        private final String name;
        private final int priority;
        private final String description;

        ErrorLevel(String name, int priority, String description) {
            this.name = name;
            this.priority = priority;
            this.description = description;
        }

        public String getName() { return name; }
        public int getPriority() { return priority; }
        public String getDescription() { return description; }
    }

    /**
     * 创建错误报告的便捷方法
     */
    public static ValidationErrorReport createError(
            int errorIndex, int rowNumber, String fieldName, String fieldDisplayName,
            Object errorValue, String errorType, String errorMessage, 
            ErrorLevel level, ErrorCategory category) {
        
        return ValidationErrorReport.builder()
                .errorIndex(errorIndex)
                .errorLevel(level.getName())
                .rowNumber(rowNumber)
                .fieldName(fieldName)
                .fieldDisplayName(fieldDisplayName)
                .errorValue(errorValue != null ? errorValue.toString() : "")
                .errorType(errorType)
                .errorMessage(errorMessage)
                .suggestion(generateSuggestion(errorType, errorValue))
                .detectionTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .category(category)
                .severity(level.getPriority())
                .build();
    }

    /**
     * 生成修正建议
     */
    private static String generateSuggestion(String errorType, Object errorValue) {
        switch (errorType) {
            case "REQUIRED_FIELD":
                return "请填写该必填字段";
            case "DATA_TYPE_MISMATCH":
                return "请检查数据类型是否正确";
            case "INVALID_FORMAT":
                return "请按照正确格式填写数据";
            case "OUT_OF_RANGE":
                return "请确保数据在有效范围内";
            case "COORDINATE_ERROR":
                return "请检查坐标格式和坐标系";
            default:
                return "请根据错误描述修正数据";
        }
    }

    /**
     * 设置原始数据行信息
     */
    public void setOriginalRowDataFromMap(java.util.Map<String, Object> dataMap) {
        this.rawData = dataMap;
        if (dataMap != null && !dataMap.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            dataMap.forEach((key, value) -> {
                if (sb.length() > 0) sb.append(" | ");
                sb.append(key).append(": ").append(value != null ? value.toString() : "null");
            });
            this.originalRowData = sb.toString();
        }
    }
}
