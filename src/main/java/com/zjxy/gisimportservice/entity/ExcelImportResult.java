package com.zjxy.gisimportservice.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Excel导入结果实体类
 * 
 * 封装Excel导入操作的结果信息，包括成功状态、统计数据、错误信息等
 * 
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExcelImportResult {

    /**
     * 导入是否成功
     */
    private boolean success;

    /**
     * 结果消息
     */
    private String message;

    /**
     * 关联的任务ID
     */
    private Long taskId;

    /**
     * 总记录数
     */
    private Integer totalRecords;

    /**
     * 成功导入记录数
     */
    private Integer successRecords;

    /**
     * 错误记录数
     */
    private Integer errorRecords;

    /**
     * 跳过记录数
     */
    private Integer skippedRecords;

    /**
     * 处理耗时（毫秒）
     */
    private Long processingTimeMs;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 错误详情列表
     */
    private List<ValidationError> errors;

    /**
     * 警告信息列表
     */
    private List<ValidationError> warnings;

    /**
     * 附加信息
     */
    private Map<String, Object> additionalInfo;

    /**
     * 文件信息
     */
    private FileInfo fileInfo;

    /**
     * 模板信息
     */
    private TemplateInfo templateInfo;

    /**
     * 验证错误内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ValidationError {
        /**
         * 错误类型
         */
        private String errorType;

        /**
         * 错误消息
         */
        private String message;

        /**
         * 行号
         */
        private Integer rowNumber;

        /**
         * 列名
         */
        private String columnName;

        /**
         * 错误值
         */
        private Object errorValue;

        /**
         * 期望值
         */
        private Object expectedValue;

        /**
         * 建议修复方案
         */
        private String suggestion;

        /**
         * 错误级别（ERROR/WARNING/INFO）
         */
        private String level;
    }

    /**
     * 文件信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FileInfo {
        /**
         * 文件名
         */
        private String fileName;

        /**
         * 文件大小（字节）
         */
        private Long fileSize;

        /**
         * 工作表名称
         */
        private String sheetName;

        /**
         * 文件类型
         */
        private String fileType;

        /**
         * 文件MD5
         */
        private String fileMd5;
    }

    /**
     * 模板信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TemplateInfo {
        /**
         * 模板ID
         */
        private Integer templateId;

        /**
         * 模板名称
         */
        private String templateName;

        /**
         * 目标表名
         */
        private String tableName;

        /**
         * 数据源名称
         */
        private String datasourceName;

        /**
         * 坐标系转换信息
         */
        private String coordinateTransform;
    }

    /**
     * 计算错误率
     * 
     * @return 错误率（百分比）
     */
    public double getErrorRate() {
        if (totalRecords == null || totalRecords == 0) {
            return 0.0;
        }
        return (errorRecords != null ? errorRecords : 0) * 100.0 / totalRecords;
    }

    /**
     * 计算成功率
     * 
     * @return 成功率（百分比）
     */
    public double getSuccessRate() {
        if (totalRecords == null || totalRecords == 0) {
            return 0.0;
        }
        return (successRecords != null ? successRecords : 0) * 100.0 / totalRecords;
    }

    /**
     * 判断是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }

    /**
     * 判断是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }

    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }

    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warnings != null ? warnings.size() : 0;
    }
}
