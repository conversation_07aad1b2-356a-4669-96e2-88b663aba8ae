package com.zjxy.gisimportservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjxy.gisimportservice.handler.ListMapToJsonHandler;
import com.zjxy.gisimportservice.handler.ListStringToJsonHandler;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * GIS模板验证配置实体类
 * 对应 gis_manage_template_valid 表
 * 与gisresourcemanage项目保持一致的结构
 */
@Data
@TableName(value = "gis_manage_template_valid", autoResultMap = true)
public class GisManageTemplateValid {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String uid;

    private String tableName;

    private String datasourceName;

    private String dataBase;

    private String dataBaseMode;

    private String dataBaseTable;

    /**
     * 关联的模板ID
     */
    @TableField("template_id")
    private Integer templateId;

    /**
     * 图片配置
     */
    @TableField(value = "tp", typeHandler = ListStringToJsonHandler.class)
    private List<String> tp;

    /**
     * 空间配置
     */
    @TableField(value = "kj", typeHandler = ListStringToJsonHandler.class)
    private List<String> kj;

    /**
     * 验证规则配置（核心字段）
     * 存储字段级验证规则的详细配置
     */
    @TableField(value = "sx", typeHandler = ListMapToJsonHandler.class)
    private List<Map<String, Object>> sx;

    /**
     * 字段映射配置
     */
    @TableField(value = "map", typeHandler = ListMapToJsonHandler.class)
    private List<Map<String, Object>> map;

    /**
     * 空间条件配置
     */
    @TableField(value = "kj_condition", typeHandler = ListMapToJsonHandler.class)
    private List<Map<String, Object>> kjCondition;
}
