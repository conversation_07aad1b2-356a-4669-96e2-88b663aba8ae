package com.zjxy.gisimportservice.controller;

import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.framework.response.Result;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.GisImportTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统一的文件导入任务控制器
 * 支持Excel、Shapefile等多种文件格式的导入
 * 基于gisresourcemanage项目架构设计
 */
@Slf4j
@RestController
@RequestMapping("/api/file-import")
@Api(tags = "文件导入任务管理")
public class FileImportTaskController {

    @Autowired
    private GisImportTaskService gisImportTaskService;

    /**
     * 创建文件导入任务（支持Excel、Shapefile等格式）
     */
    @PostMapping("/tasks")
    @ApiOperation("创建文件导入任务")
    public Result<Map<String, Object>> createTask(
            @ApiParam("上传的文件") @RequestParam("file") MultipartFile file,
            @ApiParam("模板ID") @RequestParam("templateId") Integer templateId,
            @ApiParam("任务名称") @RequestParam(value = "taskName", required = false) String taskName,
            @ApiParam("Excel表名") @RequestParam(value = "sheetName", required = false) String sheetName,
            @ApiParam("创建用户") @RequestParam("createdBy") String createdBy) {

        try {
            log.info("创建文件导入任务 - 文件: {}, 模板ID: {}, 用户: {}", file.getOriginalFilename(), templateId, createdBy);

            // 1. 文件验证
            if (file.isEmpty()) {
                return Result.FAILURE("文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                return Result.FAILURE("文件名不能为空");
            }

            // 2. 确定文件格式
            GisImportTask.ImportFormat importFormat = determineImportFormat(fileName);
            if (importFormat == null) {
                return Result.FAILURE("不支持的文件格式，请上传Excel(.xlsx/.xls)或Shapefile(.zip)文件");
            }

            // 3. 文件大小验证
            if (file.getSize() > 500 * 1024 * 1024) { // 500MB
                return Result.FAILURE("文件大小不能超过500MB");
            }

            // 4. 确定任务名称
            if (taskName == null || taskName.trim().isEmpty()) {
                taskName = importFormat.getDescription() + "导入-" + fileName;
            }

            // 5. 创建任务
            GisImportTask task = new GisImportTask();
            task.setTaskName(taskName);
            task.setTemplateId(templateId);
            task.setFileSize(file.getSize());
            task.setCreatedBy(createdBy);
            task.setImportFormat(importFormat);
            task.setDataStatus(GisImportTask.DataStatus.NOT_IMPORTED);

            // 6. 根据文件格式调用相应的创建方法
            GisImportTask createdTask;
            if (importFormat == GisImportTask.ImportFormat.EXCEL) {
                createdTask = gisImportTaskService.createExcelImportTask(task, file);
            } else if (importFormat == GisImportTask.ImportFormat.SHP) {
                createdTask = gisImportTaskService.createShapefileImportTask(task, file);
            } else {
                return Result.FAILURE("暂不支持该文件格式的导入");
            }

            // 7. 返回任务信息
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", createdTask.getId());
            result.put("taskName", createdTask.getTaskName());
            result.put("fileName", fileName);
            result.put("fileSize", createdTask.getFileSize());
            result.put("importFormat", importFormat.getDescription());
            result.put("status", createdTask.getDataStatus().getDescription());
            result.put("createdTime", createdTask.getImportTime());

            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("创建文件导入任务失败", e);
            return Result.FAILURE("创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/tasks/{taskId}")
    @ApiOperation("查询任务状态")
    public Result<GisImportTask> getTaskStatus(@ApiParam("任务ID") @PathVariable Long taskId) {
        try {
            log.info("查询任务状态 - 任务ID: {}", taskId);

            GisImportTask task = gisImportTaskService.getById(taskId);
            if (task == null) {
                return Result.FAILURE("任务不存在");
            }

            return Result.SUCCESS(task);

        } catch (Exception e) {
            log.error("查询任务状态失败", e);
            return Result.FAILURE("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务列表
     */
    @GetMapping("/tasks")
    @ApiOperation("查询任务列表")
    public Result<List<GisImportTask>> getTaskList(
            @ApiParam("创建用户") @RequestParam(value = "createdBy", required = false) String createdBy,
            @ApiParam("导入格式") @RequestParam(value = "importFormat", required = false) String importFormat,
            @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(value = "size", defaultValue = "10") Integer size) {

        try {
            log.info("查询任务列表 - 用户: {}, 格式: {}, 页码: {}, 页大小: {}", createdBy, importFormat, page, size);

            GisImportTask.ImportFormat format = null;
            if (importFormat != null && !importFormat.trim().isEmpty()) {
                try {
                    format = GisImportTask.ImportFormat.valueOf(importFormat.toUpperCase());
                } catch (IllegalArgumentException e) {
                    return Result.FAILURE("不支持的导入格式: " + importFormat);
                }
            }

            List<GisImportTask> tasks = gisImportTaskService.getTaskList(createdBy, null, format, page, size);
            return Result.SUCCESS(tasks);

        } catch (Exception e) {
            log.error("查询任务列表失败", e);
            return Result.FAILURE("查询失败: " + e.getMessage());
        }
    }

    /**
     * 文件数据验证接口（统一验证入口）
     */
    @PostMapping("/tasks/{taskId}/validate")
    @ApiOperation("文件数据验证")
    public Result<ValidationResult> validateData(@ApiParam("任务ID") @PathVariable Long taskId) {
        try {
            log.info("开始文件数据验证 - 任务ID: {}", taskId);

            // 1. 获取任务信息
            GisImportTask task = gisImportTaskService.getById(taskId);
            if (task == null) {
                return Result.FAILURE("任务不存在");
            }

            // 数据验证可以在任何时候进行，如果已经检查过就重新检查
            log.info("任务当前状态: {} - 开始数据验证", task.getDataStatus().getDescription());

            // 2. 更新任务状态为验证中
            task.setDataStatus(GisImportTask.DataStatus.DATA_UNCHECKED);
            gisImportTaskService.updateById(task);

            // 3. 根据文件格式执行相应的验证
            ValidationResult validationResult;
            if (task.getImportFormat() == GisImportTask.ImportFormat.EXCEL) {
                validationResult = gisImportTaskService.validateExcelData(task);
            } else if (task.getImportFormat() == GisImportTask.ImportFormat.SHP) {
                validationResult = gisImportTaskService.validateShapefileData(task);
            } else {
                return Result.FAILURE("不支持的文件格式验证: " + task.getImportFormat().getDescription());
            }

            // 4. 验证结果已经在Service层处理了状态更新，这里不需要重复更新
            // Service层会根据验证结果自动设置正确的状态：
            // - 验证成功：data_status = 0 (NOT_IMPORTED)
            // - 验证失败：data_status = 1 (DATA_UNCHECKED)
            log.info("验证完成，最终状态由Service层管理 - 验证结果: {}", validationResult.isPassed() ? "成功" : "失败");

            return Result.SUCCESS(validationResult);

        } catch (Exception e) {
            log.error("文件数据验证失败", e);
            // 更新任务状态为失败
            try {
                GisImportTask failedTask = gisImportTaskService.getById(taskId);
                if (failedTask != null) {
                    failedTask.setErrorMessage("验证失败: " + e.getMessage());
                    gisImportTaskService.updateById(failedTask);
                }
            } catch (Exception ex) {
                log.error("更新任务状态失败", ex);
            }
            return Result.FAILURE("验证失败: " + e.getMessage());
        }
    }

    /**
     * 文件数据导入执行接口（统一导入入口）
     */
    @PostMapping("/tasks/{taskId}/execute")
    @ApiOperation("文件数据导入执行")
    public Result<Map<String, Object>> executeImport(@ApiParam("任务ID") @PathVariable Long taskId) {
        try {
            log.info("开始文件数据导入 - 任务ID: {}", taskId);

            // 1. 获取任务信息
            GisImportTask task = gisImportTaskService.getById(taskId);
            if (task == null) {
                return Result.FAILURE("任务不存在");
            }

            // 检查任务状态是否允许导入：必须是验证完成状态(NOT_IMPORTED=0)
            if (task.getDataStatus() != GisImportTask.DataStatus.NOT_IMPORTED) {
                return Result.FAILURE("任务状态不正确，请先执行数据验证。当前状态: " + task.getDataStatus().getDescription());
            }
            task.setStartTime(new java.sql.Timestamp(System.currentTimeMillis()));
            gisImportTaskService.updateById(task);

            // 3. 根据文件格式执行相应的导入
            Map<String, Object> importResult;
            if (task.getImportFormat() == GisImportTask.ImportFormat.EXCEL) {
                importResult = gisImportTaskService.executeExcelImport(task);
            } else if (task.getImportFormat() == GisImportTask.ImportFormat.SHP) {
                importResult = gisImportTaskService.executeShapefileImport(task);
            } else {
                return Result.FAILURE("不支持的文件格式导入: " + task.getImportFormat().getDescription());
            }

            // 4. 更新任务状态为完成
            task.setEndTime(new java.sql.Timestamp(System.currentTimeMillis()));
            task.setProcessedCount((Integer) importResult.get("importedRecords"));
            gisImportTaskService.updateById(task);

            return Result.SUCCESS(importResult);

        } catch (Exception e) {
            log.error("文件数据导入失败", e);
            // 更新任务状态为失败
            try {
                GisImportTask failedTask = gisImportTaskService.getById(taskId);
                if (failedTask != null) {
                    failedTask.setErrorMessage("导入失败: " + e.getMessage());
                    failedTask.setEndTime(new java.sql.Timestamp(System.currentTimeMillis()));
                    gisImportTaskService.updateById(failedTask);
                }
            } catch (Exception ex) {
                log.error("更新任务状态失败", ex);
            }
            return Result.FAILURE("导入失败: " + e.getMessage());
        }
    }

    /**
     * 下载验证错误报告
     */
    @GetMapping("/tasks/{taskId}/error-report")
    @ApiOperation("下载验证错误报告")
    public ResponseEntity<Resource> downloadErrorReport(@ApiParam("任务ID") @PathVariable Long taskId) {
        try {
            log.info("下载验证错误报告 - 任务ID: {}", taskId);

            // 1. 获取验证结果和错误报告路径（内部会查询任务信息）
            ValidationResult validationResult = gisImportTaskService.getValidationResult(taskId);
            if (validationResult == null) {
                log.warn("任务不存在或验证结果为空 - 任务ID: {}", taskId);
                return ResponseEntity.notFound().build();
            }

            // 2. 检查是否有错误报告文件
            if (validationResult.getErrorFilePath() == null || validationResult.getErrorFilePath().trim().isEmpty()) {
                log.warn("任务没有错误报告文件 - 任务ID: {}", taskId);
                return ResponseEntity.notFound().build();
            }

            // 3. 检查错误报告文件是否存在
            Path errorFilePath = Paths.get(validationResult.getErrorFilePath());
            if (!Files.exists(errorFilePath)) {
                log.warn("错误报告文件不存在: {}", errorFilePath);
                return ResponseEntity.notFound().build();
            }

            // 4. 创建文件资源
            Resource resource = new FileSystemResource(errorFilePath.toFile());

            // 5. 设置响应头
            String fileName = String.format("validation_errors_task_%d_%s.xlsx",
                taskId,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .body(resource);

        } catch (Exception e) {
            log.error("下载验证错误报告失败 - 任务ID: {}", taskId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/tasks/{taskId}")
    @ApiOperation("删除任务")
    public Result<Void> deleteTask(@ApiParam("任务ID") @PathVariable Long taskId) {
        try {
            log.info("删除任务 - 任务ID: {}", taskId);

            boolean deleted = gisImportTaskService.removeById(taskId);
            if (deleted) {
                return Result.SUCCESS();
            } else {
                return Result.FAILURE("任务不存在或删除失败");
            }

        } catch (Exception e) {
            log.error("删除任务失败", e);
            return Result.FAILURE("删除失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件名确定导入格式
     */
    private GisImportTask.ImportFormat determineImportFormat(String fileName) {
        if (fileName == null) {
            return null;
        }

        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".xlsx") || lowerFileName.endsWith(".xls")) {
            return GisImportTask.ImportFormat.EXCEL;
        } else if (lowerFileName.endsWith(".zip") || lowerFileName.endsWith(".shp")) {
            return GisImportTask.ImportFormat.SHP;
        } else if (lowerFileName.endsWith(".dwg") || lowerFileName.endsWith(".dxf")) {
            return GisImportTask.ImportFormat.CAD;
        } else {
            return null;
        }
    }
}
