package com.zjxy.gisimportservice.controller;

import com.zjxy.gisimportservice.config.FileUploadConfig;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.service.GisImportTaskService;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import com.zjxy.gisimportservice.service.TemplateBasedShapefileService;
import com.zjxy.gisimportservice.service.Impl.ShapefileReaderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

/**
 * 基于模板的Shapefile处理控制器（专用功能）
 *
 * 提供Shapefile特有的处理功能和模板管理
 *
 * 注意：导入任务管理功能已迁移到统一架构：
 * - 任务管理：使用 FileImportTaskController (/api/file-import/tasks/*)
 * - 数据验证：使用 FileImportTaskController (/api/file-import/tasks/{taskId}/validate)
 *
 * 本控制器保留Shapefile特有功能：
 * - Shapefile文件处理 (/upload-with-template, /process-with-template)
 * - 模板管理 (/templates/*)
 */
@Slf4j
@RestController
@RequestMapping("/api/template-shapefile")
public class TemplateBasedShapefileController {

    @Autowired
    private TemplateBasedShapefileService templateBasedShapefileService;


    @Autowired
    private GisImportTaskService gisImportTaskService;


    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Autowired
    private GisManageTemplateService templateService;


    /**
     * 使用模板上传并处理Shapefile
     */
    @PostMapping("/upload-with-template")
    public ResponseEntity<Map<String, Object>> uploadShapefileWithTemplate(
            @RequestParam("file") MultipartFile file,
            @RequestParam("templateId") Integer templateId) {

        Map<String, Object> response = new HashMap<>();

        if (file.isEmpty()) {
            response.put("success", false);
            response.put("message", "请选择一个ZIP文件上传");
            return ResponseEntity.badRequest().body(response);
        }

        if (templateId == null) {
            response.put("success", false);
            response.put("message", "请指定模板ID");
            return ResponseEntity.badRequest().body(response);
        }

        try {
            log.info("开始使用模板处理Shapefile，模板ID: {}, 文件名: {}", templateId, file.getOriginalFilename());

            // 使用模板处理Shapefile
            Map<String, Object> result = templateBasedShapefileService.processShapefileWithTemplate(
                file.getInputStream(),
                file.getOriginalFilename(),
                templateId
            );

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("使用模板处理Shapefile出错", e);
            response.put("success", false);
            response.put("message", "处理Shapefile出错: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 从路径使用模板处理Shapefile
     */
    @PostMapping("/process-with-template")
    public ResponseEntity<Map<String, Object>> processShapefileWithTemplate(

            @RequestParam("filePath") String filePath,
            @RequestParam("templateId") Integer templateId) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("从路径使用模板处理Shapefile，模板ID: {}, 文件路径: {}", templateId, filePath);

            // 使用模板处理Shapefile
            Map<String, Object> result = templateBasedShapefileService.processShapefileWithTemplateFromPath(filePath, templateId);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("使用模板处理Shapefile出错", e);
            response.put("success", false);
            response.put("message", "处理Shapefile出错: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取所有导入模板
     */
    @GetMapping("/templates")
    public ResponseEntity<Map<String, Object>> getAllImportTemplates() {
        Map<String, Object> response = new HashMap<>();

        try {
            List<GisManageTemplate> templates = templateService.getAllImportTemplates();

            response.put("success", true);
            response.put("data", templates);
            response.put("total", templates.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取模板列表失败", e);
            response.put("success", false);
            response.put("message", "获取模板列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据ID获取模板详情
     */
    @GetMapping("/templates/{templateId}")
    public ResponseEntity<Map<String, Object>> getTemplateById(@PathVariable Integer templateId) {
        Map<String, Object> response = new HashMap<>();

        try {
            GisManageTemplate template = templateService.getTemplateById(templateId);

            // 添加调试日志，检查JSON字段是否为空
            log.info("模板详情 - ID: {}, 名称: {}", template.getId(), template.getNameZh());
            log.info("JSON字段检查:");
            log.info("  map: {}", template.getMap() != null ? "有数据" : "为空");
            log.info("  lineMap: {}", template.getLineMap() != null ? "有数据" : "为空");
            log.info("  pointMap: {}", template.getPointMap() != null ? "有数据" : "为空");
            log.info("  valueMap: {}", template.getValueMap() != null ? "有数据" : "为空");
            log.info("  association: {}", template.getAssociation() != null ? "有数据" : "为空");

            if (template.getMap() != null) {
                log.info("  map内容: {}", template.getMap());
            }

            response.put("success", true);
            response.put("data", template);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取模板详情失败，模板ID: {}", templateId, e);
            response.put("success", false);
            response.put("message", "获取模板详情失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 创建新模板
     */
    @PostMapping("/templates")
    public ResponseEntity<Map<String, Object>> createTemplate(@RequestBody GisManageTemplate template) {
        Map<String, Object> response = new HashMap<>();

        try {
            Integer templateId = templateService.createTemplate(template);

            response.put("success", true);
            response.put("message", "模板创建成功");
            response.put("templateId", templateId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("创建模板失败", e);
            response.put("success", false);
            response.put("message", "创建模板失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新模板
     */
    @PutMapping("/templates/{templateId}")
    public ResponseEntity<Map<String, Object>> updateTemplate(
            @PathVariable Integer templateId,
            @RequestBody GisManageTemplate template) {

        Map<String, Object> response = new HashMap<>();

        try {
            template.setId(templateId);
            Boolean success = templateService.updateTemplate(template);

            response.put("success", success);
            response.put("message", success ? "模板更新成功" : "模板更新失败");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("更新模板失败，模板ID: {}", templateId, e);
            response.put("success", false);
            response.put("message", "更新模板失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 删除模板
     */
    @DeleteMapping("/templates/{templateId}")
    public ResponseEntity<Map<String, Object>> deleteTemplate(@PathVariable Integer templateId) {
        Map<String, Object> response = new HashMap<>();

        try {
            Boolean success = templateService.deleteTemplate(templateId);

            response.put("success", success);
            response.put("message", success ? "模板删除成功" : "模板删除失败");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("删除模板失败，模板ID: {}", templateId, e);
            response.put("success", false);
            response.put("message", "删除模板失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据表名获取模板列表
     */
    @GetMapping("/templates/by-table-name/{tableName}")
    public ResponseEntity<Map<String, Object>> getTemplatesByTableName(@PathVariable String tableName) {
        Map<String, Object> response = new HashMap<>();

        try {
            List<GisManageTemplate> templates = templateService.getTemplatesByTableName(tableName);

            response.put("success", true);
            response.put("data", templates);
            response.put("total", templates.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("根据表名获取模板列表失败，表名: {}", tableName, e);
            response.put("success", false);
            response.put("message", "获取模板列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }


    // ========== 导入任务管理API ==========//
    // 注意：导入任务管理功能已迁移到 FileImportTaskController
    // 请使用以下统一接口：
    // - 创建任务：POST /api/file-import/tasks
    // - 查询任务：GET /api/file-import/tasks/{taskId}
    // - 验证数据：POST /api/file-import/tasks/{taskId}/validate
    // - 执行导入：POST /api/file-import/tasks/{taskId}/execute
    // - 删除任务：DELETE /api/file-import/tasks/{taskId}

    // 注意：获取任务列表功能已迁移到 FileImportTaskController
    // 请使用：GET /api/file-import/tasks

    // 注意：获取任务详情功能已迁移到 FileImportTaskController
    // 请使用：GET /api/file-import/tasks/{taskId}

    // 注意：更新任务状态功能已迁移到 FileImportTaskController
    // 请使用：PUT /api/file-import/tasks/{taskId}/status

    // 注意：删除任务功能已迁移到 FileImportTaskController
    // 请使用：DELETE /api/file-import/tasks/{taskId}

    /**
     * 获取任务统计信息
     */
    @GetMapping("/import-tasks/statistics")
    public ResponseEntity<Map<String, Object>> getTaskStatistics() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> statistics = gisImportTaskService.getTaskStatistics();
            response.put("success", true);
            response.putAll(statistics);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取任务统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据文件名确定导入格式
     */
    private GisImportTask.ImportFormat determineImportFormat(String fileName) {
        if (fileName == null) {
            return GisImportTask.ImportFormat.SHP;
        }

        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".zip") || lowerFileName.endsWith(".shp")) {
            return GisImportTask.ImportFormat.SHP;
        } else if (lowerFileName.endsWith(".xlsx") || lowerFileName.endsWith(".xls")) {
            return GisImportTask.ImportFormat.EXCEL;
        } else if (lowerFileName.endsWith(".dwg") || lowerFileName.endsWith(".dxf")) {
            return GisImportTask.ImportFormat.CAD;
        } else {
            return GisImportTask.ImportFormat.SHP; // 默认
        }
    }

    /**
     * 保存上传的文件到临时目录
     */
    private String saveUploadedFileToTemp(MultipartFile file) throws IOException {
        // 检查配置是否可用
        if (!fileUploadConfig.isDirectoryAvailable()) {
            throw new IOException("上传目录不可用: " + fileUploadConfig.getTempDir());
        }

        // 使用配置的上传目录
        Path uploadDir = Paths.get(fileUploadConfig.getTempDir());

        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
            log.info("创建上传目录: {}", uploadDir.toAbsolutePath());
        }

        // 生成唯一的文件名（保留原始扩展名）
        String originalFilename = file.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        String fileName = System.currentTimeMillis() + "_" +
                         (originalFilename != null ? originalFilename : "upload" + extension);
        Path filePath = uploadDir.resolve(fileName);

        // 保存文件
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

        String savedPath = filePath.toAbsolutePath().toString();
        log.info("文件保存成功: {} -> {}", originalFilename, savedPath);

        return savedPath;
    }

}
