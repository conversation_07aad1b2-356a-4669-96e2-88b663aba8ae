package com.zjxy.gisimportservice.controller;

import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.config.ExcelImportConfig;
import com.zjxy.gisimportservice.entity.ExcelImportResult;
import com.zjxy.gisimportservice.monitor.ExcelImportMetrics;
import com.zjxy.gisimportservice.service.ExcelImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel导入控制器（专用功能）
 *
 * 提供Excel文件特有的分析、批量导入、监控等功能
 *
 * 注意：主要的文件导入和验证功能已迁移到统一架构：
 * - 文件导入：使用 FileImportTaskController (/api/file-import/tasks)
 * - 数据验证：使用 FileImportTaskController (/api/file-import/tasks/{taskId}/validate)
 * - 任务管理：使用 TemplateBasedShapefileController (/api/template-shapefile/import-task/*)
 *
 * 本控制器保留Excel特有功能：
 * - 文件结构分析 (/analyze, /sheets)
 * - 批量导入优化 (/batch-import)
 * - 附件数据分析 (/analyze-attachment)
 * - 性能监控统计 (/metrics, /active-tasks)
 * - 健康检查配置 (/health, /config)
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@Slf4j
@RestController
@RequestMapping("/api/excel-import")
@Api(tags = "Excel专用功能") // Excel特有功能的API文档
public class ExcelImportController {

    @Autowired
    private ExcelImportService excelImportService;

    @Autowired
    private ExcelImportConfig excelConfig;

    @Autowired
    private ExcelImportMetrics metrics;


    /**
     * 分析Excel文件结构
     */
    @PostMapping("/analyze")
    @ApiOperation("分析Excel文件结构")
    public ResponseEntity<Map<String, Object>> analyzeExcel(
            @ApiParam("Excel文件") @RequestParam("file") MultipartFile file,
            @ApiParam("表头行号") @RequestParam(value = "headerRow", defaultValue = "1") Integer headerRow) {

        Map<String, Object> response = new HashMap<>();

        try {
            DynamicDataSourceManager.build().useDataSource("slave");

            log.info("接收Excel分析请求 - 文件: {}, 表头行: {}", file.getOriginalFilename(), headerRow);

            Map<String, Object> analysis = excelImportService.analyzeExcelFile(file, headerRow);

            response.put("success", true);
            response.put("message", "Excel文件分析成功");
            response.put("data", analysis);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Excel文件分析失败", e);
            response.put("success", false);
            response.put("message", "Excel文件分析失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取Excel工作表名称列表
     */
    @PostMapping("/sheets")
    @ApiOperation("获取Excel工作表名称列表")
    public ResponseEntity<Map<String, Object>> getSheetNames(
            @ApiParam("Excel文件") @RequestParam("file") MultipartFile file) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("获取Excel工作表名称 - 文件: {}", file.getOriginalFilename());

            List<String> sheetNames = excelImportService.getSheetNames(file);

            response.put("success", true);
            response.put("message", "获取工作表名称成功");
            response.put("data", sheetNames);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取Excel工作表名称失败", e);
            response.put("success", false);
            response.put("message", "获取工作表名称失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }


    /**
     * 分析Excel附件数据
     */
    @PostMapping("/analyze-attachment")
    @ApiOperation("分析Excel附件数据")
    public ResponseEntity<Map<String, Object>> analyzeExcelAttachment(
            @ApiParam("Excel文件") @RequestParam("file") MultipartFile file,
            @ApiParam("模板ID") @RequestParam("templateId") Integer templateId) {

        Map<String, Object> response = new HashMap<>();

        try {
            DynamicDataSourceManager.build().useDataSource("slave");

            log.info("接收Excel附件分析请求 - 文件: {}, 模板ID: {}", file.getOriginalFilename(), templateId);

            Map<String, List<Map<String, Object>>> attachmentData =
                    excelImportService.analyzeExcelAttachment(file, templateId);

            response.put("success", true);
            response.put("message", "Excel附件数据分析成功");
            response.put("data", attachmentData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Excel附件数据分析失败", e);
            response.put("success", false);
            response.put("message", "Excel附件数据分析失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量导入Excel数据
     */
    @PostMapping("/batch-import")
    @ApiOperation("批量导入Excel数据")
    public ResponseEntity<Map<String, Object>> batchImportExcel(
            @ApiParam("Excel文件") @RequestParam("file") MultipartFile file,
            @ApiParam("模板ID") @RequestParam("templateId") Integer templateId,
            @ApiParam("批次大小") @RequestParam(value = "batchSize", required = false) Integer batchSize,
            @ApiParam("导入目标类型") @RequestParam(value = "target", defaultValue = "import") String target,
            @ApiParam("创建用户") @RequestParam(value = "createdBy", required = false) String createdBy) {

        Map<String, Object> response = new HashMap<>();

        try {
            // 验证文件
            if (file.isEmpty()) {
                response.put("success", false);
                response.put("message", "请选择一个文件上传");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证文件格式
            if (!excelConfig.isSupportedFormat(file.getOriginalFilename())) {
                response.put("success", false);
                response.put("message", "不支持的文件格式，支持的格式: " + excelConfig.getSupportedFormats());
                return ResponseEntity.badRequest().body(response);
            }

            // 验证文件大小
            if (!excelConfig.isFileSizeValid(file.getSize())) {
                response.put("success", false);
                response.put("message", "文件大小超过限制，最大允许: " + excelConfig.getMaxFileSize() + "MB");
                return ResponseEntity.badRequest().body(response);
            }


            // 使用配置的默认批次大小
            if (batchSize == null) {
                batchSize = excelConfig.getDefaultBatchSize();
            }

            log.info("接收Excel批量导入请求 - 文件: {}, 模板ID: {}, 批次大小: {}",
                    file.getOriginalFilename(), templateId, batchSize);

            ExcelImportResult result = excelImportService.batchImportExcelData(
                    file, templateId, batchSize, target, createdBy);

            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("data", result);

            if (result.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            log.error("Excel批量导入失败", e);
            response.put("success", false);
            response.put("message", "Excel批量导入失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取Excel导入配置信息
     */
    @GetMapping("/config")
    @ApiOperation("获取Excel导入配置信息")
    public ResponseEntity<Map<String, Object>> getExcelImportConfig() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> configInfo = new HashMap<>();
            configInfo.put("enabled", excelConfig.isEnabled());
            configInfo.put("maxFileSize", excelConfig.getMaxFileSize());
            configInfo.put("maxFileSizeBytes", excelConfig.getMaxFileSizeBytes());
            configInfo.put("supportedFormats", excelConfig.getSupportedFormats());
            configInfo.put("defaultBatchSize", excelConfig.getDefaultBatchSize());
            configInfo.put("processing", excelConfig.getProcessing());
            configInfo.put("validation", excelConfig.getValidation());

            response.put("success", true);
            response.put("message", "获取配置信息成功");
            response.put("data", configInfo);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取Excel导入配置失败", e);
            response.put("success", false);
            response.put("message", "获取配置信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @ApiOperation("Excel导入服务健康检查")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();

        try {
            response.put("success", true);
            response.put("message", "Excel导入服务运行正常");
            response.put("timestamp", System.currentTimeMillis());
            response.put("enabled", excelConfig.isEnabled());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Excel导入服务健康检查失败", e);
            response.put("success", false);
            response.put("message", "服务异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取性能统计信息
     */
    @GetMapping("/metrics")
    @ApiOperation("获取Excel导入性能统计信息")
    public ResponseEntity<Map<String, Object>> getMetrics() {
        Map<String, Object> response = new HashMap<>();

        try {
            ExcelImportMetrics.ImportStatistics stats = metrics.getStatistics();

            response.put("success", true);
            response.put("message", "获取统计信息成功");
            response.put("data", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取Excel导入统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取活跃任务列表
     */
    @GetMapping("/active-tasks")
    @ApiOperation("获取当前活跃的Excel导入任务")
    public ResponseEntity<Map<String, Object>> getActiveTasks() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, ExcelImportMetrics.ImportTask> activeTasks = metrics.getActiveTasks();

            response.put("success", true);
            response.put("message", "获取活跃任务成功");
            response.put("data", activeTasks);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取活跃任务失败", e);
            response.put("success", false);
            response.put("message", "获取活跃任务失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 重置统计信息
     */
    @PostMapping("/reset-metrics")
    @ApiOperation("重置Excel导入统计信息")
    public ResponseEntity<Map<String, Object>> resetMetrics() {
        Map<String, Object> response = new HashMap<>();

        try {
            metrics.resetStatistics();

            response.put("success", true);
            response.put("message", "统计信息重置成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("重置统计信息失败", e);
            response.put("success", false);
            response.put("message", "重置统计信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
