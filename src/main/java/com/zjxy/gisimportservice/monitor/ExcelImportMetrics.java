package com.zjxy.gisimportservice.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Excel导入性能监控组件
 * 
 * 提供Excel导入过程的性能指标收集和监控功能
 * 
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@Slf4j
@Component
public class ExcelImportMetrics {

    // 导入次数统计
    private final AtomicLong totalImports = new AtomicLong(0);
    private final AtomicLong successfulImports = new AtomicLong(0);
    private final AtomicLong failedImports = new AtomicLong(0);

    // 记录数统计
    private final AtomicLong totalRecordsProcessed = new AtomicLong(0);
    private final AtomicLong totalRecordsSuccess = new AtomicLong(0);
    private final AtomicLong totalRecordsError = new AtomicLong(0);

    // 处理时间统计
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    private final AtomicLong minProcessingTime = new AtomicLong(Long.MAX_VALUE);

    // 文件大小统计
    private final AtomicLong totalFileSize = new AtomicLong(0);
    private final AtomicLong maxFileSize = new AtomicLong(0);

    // 当前活跃导入任务
    private final ConcurrentHashMap<String, ImportTask> activeTasks = new ConcurrentHashMap<>();

    /**
     * 导入任务信息
     */
    public static class ImportTask {
        private final String taskId;
        private final String fileName;
        private final long fileSize;
        private final LocalDateTime startTime;
        private final String templateId;

        public ImportTask(String taskId, String fileName, long fileSize, String templateId) {
            this.taskId = taskId;
            this.fileName = fileName;
            this.fileSize = fileSize;
            this.templateId = templateId;
            this.startTime = LocalDateTime.now();
        }

        // Getters
        public String getTaskId() { return taskId; }
        public String getFileName() { return fileName; }
        public long getFileSize() { return fileSize; }
        public LocalDateTime getStartTime() { return startTime; }
        public String getTemplateId() { return templateId; }
    }

    /**
     * 记录导入开始
     */
    public void recordImportStart(String taskId, String fileName, long fileSize, String templateId) {
        totalImports.incrementAndGet();
        totalFileSize.addAndGet(fileSize);
        
        if (fileSize > maxFileSize.get()) {
            maxFileSize.set(fileSize);
        }

        ImportTask task = new ImportTask(taskId, fileName, fileSize, templateId);
        activeTasks.put(taskId, task);

        log.info("Excel导入开始 - 任务ID: {}, 文件: {}, 大小: {} bytes, 模板: {}", 
                taskId, fileName, fileSize, templateId);
    }

    /**
     * 记录导入完成
     */
    public void recordImportComplete(String taskId, boolean success, long processingTimeMs, 
                                   int totalRecords, int successRecords, int errorRecords) {
        
        ImportTask task = activeTasks.remove(taskId);
        
        if (success) {
            successfulImports.incrementAndGet();
        } else {
            failedImports.incrementAndGet();
        }

        // 更新处理时间统计
        totalProcessingTime.addAndGet(processingTimeMs);
        if (processingTimeMs > maxProcessingTime.get()) {
            maxProcessingTime.set(processingTimeMs);
        }
        if (processingTimeMs < minProcessingTime.get()) {
            minProcessingTime.set(processingTimeMs);
        }

        // 更新记录数统计
        totalRecordsProcessed.addAndGet(totalRecords);
        totalRecordsSuccess.addAndGet(successRecords);
        totalRecordsError.addAndGet(errorRecords);

        log.info("Excel导入完成 - 任务ID: {}, 成功: {}, 耗时: {}ms, 总记录: {}, 成功: {}, 错误: {}", 
                taskId, success, processingTimeMs, totalRecords, successRecords, errorRecords);

        // 定期输出统计信息
        if (totalImports.get() % 10 == 0) {
            logStatistics();
        }
    }

    /**
     * 记录导入错误
     */
    public void recordImportError(String taskId, String errorMessage) {
        activeTasks.remove(taskId);
        failedImports.incrementAndGet();
        
        log.error("Excel导入失败 - 任务ID: {}, 错误: {}", taskId, errorMessage);
    }

    /**
     * 获取当前统计信息
     */
    public ImportStatistics getStatistics() {
        ImportStatistics stats = new ImportStatistics();
        
        stats.totalImports = totalImports.get();
        stats.successfulImports = successfulImports.get();
        stats.failedImports = failedImports.get();
        stats.successRate = stats.totalImports > 0 ? 
                (double) stats.successfulImports / stats.totalImports * 100 : 0;

        stats.totalRecordsProcessed = totalRecordsProcessed.get();
        stats.totalRecordsSuccess = totalRecordsSuccess.get();
        stats.totalRecordsError = totalRecordsError.get();
        stats.recordSuccessRate = stats.totalRecordsProcessed > 0 ? 
                (double) stats.totalRecordsSuccess / stats.totalRecordsProcessed * 100 : 0;

        stats.totalProcessingTime = totalProcessingTime.get();
        stats.averageProcessingTime = stats.totalImports > 0 ? 
                (double) stats.totalProcessingTime / stats.totalImports : 0;
        stats.maxProcessingTime = maxProcessingTime.get();
        stats.minProcessingTime = minProcessingTime.get() == Long.MAX_VALUE ? 0 : minProcessingTime.get();

        stats.totalFileSize = totalFileSize.get();
        stats.averageFileSize = stats.totalImports > 0 ? 
                (double) stats.totalFileSize / stats.totalImports : 0;
        stats.maxFileSize = maxFileSize.get();

        stats.activeTaskCount = activeTasks.size();

        return stats;
    }

    /**
     * 获取活跃任务列表
     */
    public ConcurrentHashMap<String, ImportTask> getActiveTasks() {
        return new ConcurrentHashMap<>(activeTasks);
    }

    /**
     * 输出统计信息到日志
     */
    public void logStatistics() {
        ImportStatistics stats = getStatistics();
        
        log.info("=== Excel导入统计信息 ===");
        log.info("总导入次数: {}, 成功: {}, 失败: {}, 成功率: {:.2f}%", 
                stats.totalImports, stats.successfulImports, stats.failedImports, stats.successRate);
        log.info("总处理记录: {}, 成功: {}, 错误: {}, 成功率: {:.2f}%", 
                stats.totalRecordsProcessed, stats.totalRecordsSuccess, stats.totalRecordsError, stats.recordSuccessRate);
        log.info("处理时间 - 总计: {}ms, 平均: {:.2f}ms, 最大: {}ms, 最小: {}ms", 
                stats.totalProcessingTime, stats.averageProcessingTime, stats.maxProcessingTime, stats.minProcessingTime);
        log.info("文件大小 - 总计: {} bytes, 平均: {:.2f} bytes, 最大: {} bytes", 
                stats.totalFileSize, stats.averageFileSize, stats.maxFileSize);
        log.info("当前活跃任务数: {}", stats.activeTaskCount);
        log.info("========================");
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalImports.set(0);
        successfulImports.set(0);
        failedImports.set(0);
        totalRecordsProcessed.set(0);
        totalRecordsSuccess.set(0);
        totalRecordsError.set(0);
        totalProcessingTime.set(0);
        maxProcessingTime.set(0);
        minProcessingTime.set(Long.MAX_VALUE);
        totalFileSize.set(0);
        maxFileSize.set(0);
        activeTasks.clear();
        
        log.info("Excel导入统计信息已重置");
    }

    /**
     * 统计信息数据类
     */
    public static class ImportStatistics {
        public long totalImports;
        public long successfulImports;
        public long failedImports;
        public double successRate;

        public long totalRecordsProcessed;
        public long totalRecordsSuccess;
        public long totalRecordsError;
        public double recordSuccessRate;

        public long totalProcessingTime;
        public double averageProcessingTime;
        public long maxProcessingTime;
        public long minProcessingTime;

        public long totalFileSize;
        public double averageFileSize;
        public long maxFileSize;

        public int activeTaskCount;
    }
}
