package com.zjxy.gisimportservice.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;

import java.util.List;
import java.util.Map;

/**
 * List<Map<String,Object>> 类型的JSON处理器
 */
public class ListMapToJsonHandler extends AbstractJsonTypeHandler<List<Map<String,Object>>> {
    @Override
    protected List<Map<String, Object>> parse(String json) {
        return JSONObject.parseObject(json,new TypeReference<List<Map<String,Object>>>(){});
    }

    @Override
    protected String toJson(List<Map<String, Object>> obj) {
        return JSON.toJSONString(obj);
    }
}
