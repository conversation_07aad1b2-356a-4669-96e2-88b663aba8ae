package com.zjxy.gisimportservice.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;

import java.util.Map;

/**
 * Map<String,Object> 类型的JSON处理器
 * 与gisresourcemanage项目保持一致
 */
public class MapStringJsonTypeHandler extends AbstractJsonTypeHandler<Map<String,Object>> {
    @Override
    protected Map<String, Object> parse(String json) {
        return JSON.parseObject(json,new TypeReference<Map<String,Object>>(){});
    }

    @Override
    protected String toJson(Map<String, Object> obj) {
        return JSON.toJSONString(obj);
    }
}
