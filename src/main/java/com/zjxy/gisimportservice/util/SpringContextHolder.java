package com.zjxy.gisimportservice.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Spring上下文工具类
 * 用于在非Spring管理的类中获取Spring Bean
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-29
 */
@Slf4j
@Component
public class SpringContextHolder implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    /**
     * 设置应用上下文
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringContextHolder.applicationContext = applicationContext;
        log.info("SpringContextHolder初始化完成");
    }

    /**
     * 获取应用上下文
     */
    public static ApplicationContext getApplicationContext() {
        assertApplicationContext();
        return applicationContext;
    }

    /**
     * 根据Bean名称获取Bean
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String beanName) {
        assertApplicationContext();
        return (T) applicationContext.getBean(beanName);
    }

    /**
     * 根据Bean类型获取Bean
     */
    public static <T> T getBean(Class<T> requiredType) {
        assertApplicationContext();
        return applicationContext.getBean(requiredType);
    }

    /**
     * 根据Bean名称和类型获取Bean
     */
    public static <T> T getBean(String beanName, Class<T> requiredType) {
        assertApplicationContext();
        return applicationContext.getBean(beanName, requiredType);
    }

    /**
     * 检查ApplicationContext是否已注入
     */
    private static void assertApplicationContext() {
        if (applicationContext == null) {
            throw new RuntimeException("ApplicationContext未注入，请确保SpringContextHolder已被Spring管理");
        }
    }

    /**
     * 清除ApplicationContext
     */
    public static void clear() {
        log.debug("清除SpringContextHolder中的ApplicationContext");
        applicationContext = null;
    }
}
