package com.zjxy.gisimportservice.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * Excel工具类
 *
 * 提供Excel文件读取、解析、数据转换等功能
 * 适配gisimportservice项目的GeoFeatureEntity数据结构
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@Slf4j
public class ExcelUtil {

    /**
     * Excel工作表信息
     */
    @Setter
    @Getter
    public static class ExcelSheet {
        private String name;
        private List<ExcelRow> rows;
        private int totalRows;

        // 构造函数、getter、setter
        public ExcelSheet() {
            this.rows = new ArrayList<>();
        }

    }

    /**
     * Excel行信息
     */
    @Setter
    @Getter
    public static class ExcelRow {
        private int index;
        private List<ExcelCell> cells;
        private ExcelSheet sheet;

        public ExcelRow() {
            this.cells = new ArrayList<>();
        }

    }

    /**
     * Excel单元格信息
     */
    @Setter
    @Getter
    public static class ExcelCell {
        private int columnIndex;
        private Object value;
        private String stringValue;
        private CellType cellType;

    }

    /**
     * 读取Excel文件并转换为GeoFeatureEntity列表
     *
     * @param inputStream Excel文件输入流
     * @param template 模板配置
     * @return GeoFeatureEntity列表
     * @throws IOException 文件读取异常
     */
    public static List<GeoFeatureEntity> readExcelToGeoFeatures(
            InputStream inputStream,
            GisManageTemplate template) throws IOException {

        List<GeoFeatureEntity> entities = new ArrayList<>();

        try {
            // 使用EasyExcel读取数据
            List<Map<Integer, Object>> dataList = new ArrayList<>();

            EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, Object>>() {
                @Override
                public void invoke(Map<Integer, Object> data, AnalysisContext context) {
                    dataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel数据读取完成，共读取 {} 行数据", dataList.size());
                }
            }).sheet(template.getSheetName()).headRowNumber(template.getThLine()).doRead();

            // 转换为GeoFeatureEntity
            entities = convertToGeoFeatureEntities(dataList, template);

        } catch (Exception e) {
            log.error("读取Excel文件失败", e);
            throw new IOException("读取Excel文件失败: " + e.getMessage(), e);
        }

        return entities;
    }

    /**
     * 获取Excel文件的工作表名称列表
     *
     * @param file Excel文件
     * @return 工作表名称列表
     * @throws IOException 文件读取异常
     */
    public static List<String> getSheetNames(MultipartFile file) throws IOException {
        List<String> sheetNames = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                sheetNames.add(sheet.getSheetName());
            }

        } catch (Exception e) {
            log.error("获取Excel工作表名称失败", e);
            throw new IOException("获取Excel工作表名称失败: " + e.getMessage(), e);
        }

        return sheetNames;
    }

    /**
     * 分析Excel文件结构
     *
     * @param file Excel文件
     * @param headerRow 表头行号
     * @return 分析结果
     * @throws IOException 文件读取异常
     */
    public static Map<String, Object> analyzeExcelStructure(
            MultipartFile file,
            Integer headerRow) throws IOException {

        Map<String, Object> result = new HashMap<>();

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            List<Map<String, Object>> sheets = new ArrayList<>();

            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                Map<String, Object> sheetInfo = analyzeSheet(sheet, headerRow);
                sheets.add(sheetInfo);
            }

            result.put("sheets", sheets);
            result.put("totalSheets", workbook.getNumberOfSheets());
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());

        } catch (Exception e) {
            log.error("分析Excel文件结构失败", e);
            throw new IOException("分析Excel文件结构失败: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 分析单个工作表
     *
     * @param sheet 工作表
     * @param headerRow 表头行号
     * @return 工作表分析结果
     */
    private static Map<String, Object> analyzeSheet(Sheet sheet, Integer headerRow) {
        Map<String, Object> sheetInfo = new HashMap<>();

        sheetInfo.put("name", sheet.getSheetName());
        sheetInfo.put("totalRows", sheet.getLastRowNum() + 1);

        // 分析表头
        if (headerRow != null && headerRow > 0 && headerRow <= sheet.getLastRowNum() + 1) {
            Row header = sheet.getRow(headerRow - 1);
            if (header != null) {
                List<String> headers = new ArrayList<>();
                for (int i = 0; i < header.getLastCellNum(); i++) {
                    Cell cell = header.getCell(i);
                    String headerValue = cell != null ? getCellStringValue(cell) : "";
                    headers.add(headerValue);
                }
                sheetInfo.put("headers", headers);
                sheetInfo.put("totalColumns", headers.size());
            }
        }

        // 分析数据预览（前5行）
        List<List<String>> preview = new ArrayList<>();
        int previewRows = Math.min(5, sheet.getLastRowNum() + 1);
        for (int i = 0; i < previewRows; i++) {
            Row row = sheet.getRow(i);
            List<String> rowData = new ArrayList<>();
            if (row != null) {
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    String cellValue = cell != null ? getCellStringValue(cell) : "";
                    rowData.add(cellValue);
                }
            }
            preview.add(rowData);
        }
        sheetInfo.put("preview", preview);

        return sheetInfo;
    }

    /**
     * 获取单元格字符串值
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private static String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 将Excel数据转换为GeoFeatureEntity列表
     *
     * @param dataList Excel数据列表
     * @param template 模板配置
     * @return GeoFeatureEntity列表
     */
    private static List<GeoFeatureEntity> convertToGeoFeatureEntities(
            List<Map<Integer, Object>> dataList,
            GisManageTemplate template) {

        List<GeoFeatureEntity> entities = new ArrayList<>();

        // 获取模板字段映射配置
        List<Map<String, Object>> fieldMappings = template.getMap();
        if (fieldMappings == null || fieldMappings.isEmpty()) {
            log.warn("模板字段映射配置为空，模板ID: {}", template.getId());
            return entities;
        }

        for (int i = 0; i < dataList.size(); i++) {
            Map<Integer, Object> rowData = dataList.get(i);

            try {
                GeoFeatureEntity entity = convertRowToGeoFeatureEntity(rowData, fieldMappings, i + 1);
                if (entity != null) {
                    entities.add(entity);
                }
            } catch (Exception e) {
                log.error("转换第 {} 行数据失败: {}", i + 1, e.getMessage());
            }
        }

        return entities;
    }

    /**
     * 将单行数据转换为GeoFeatureEntity
     *
     * @param rowData 行数据
     * @param fieldMappings 字段映射配置
     * @param rowNumber 行号
     * @return GeoFeatureEntity
     */
    private static GeoFeatureEntity convertRowToGeoFeatureEntity(
            Map<Integer, Object> rowData,
            List<Map<String, Object>> fieldMappings,
            int rowNumber) {

        GeoFeatureEntity entity = new GeoFeatureEntity();
        entity.setFeatureId("excel_row_" + rowNumber);

        Map<String, Object> attributes = new HashMap<>();

        // 根据字段映射配置转换数据
        for (Map<String, Object> mapping : fieldMappings) {
            Integer position = (Integer) mapping.get("position");
            String fieldName = (String) mapping.get("fieldName");
            String fieldType = (String) mapping.get("fieldType");

            if (position != null && fieldName != null && rowData.containsKey(position)) {
                Object value = rowData.get(position);
                Object convertedValue = convertValue(value, fieldType);
                attributes.put(fieldName, convertedValue);
            }
        }

        entity.setRawAttributes(attributes);

        return entity;
    }

    /**
     * 根据字段类型转换值
     *
     * @param value 原始值
     * @param fieldType 字段类型
     * @return 转换后的值
     */
    private static Object convertValue(Object value, String fieldType) {
        if (value == null) {
            return null;
        }

        try {
            switch (fieldType.toLowerCase()) {
                case "string":
                case "varchar":
                case "text":
                    return value.toString();
                case "integer":
                case "int":
                    return Integer.valueOf(value.toString());
                case "double":
                case "float":
                case "decimal":
                    return Double.valueOf(value.toString());
                case "boolean":
                    return Boolean.valueOf(value.toString());
                default:
                    return value;
            }
        } catch (Exception e) {
            log.warn("值转换失败，原始值: {}, 目标类型: {}, 错误: {}", value, fieldType, e.getMessage());
            return value;
        }
    }

    /**
     * 将列索引转换为Excel列名（A, B, C, ..., AA, AB, ...）
     *
     * @param columnIndex 列索引（从0开始）
     * @return Excel列名
     */
    public static String convertTo26Base(int columnIndex) {
        StringBuilder result = new StringBuilder();
        while (columnIndex >= 0) {
            result.insert(0, (char) ('A' + columnIndex % 26));
            columnIndex = columnIndex / 26 - 1;
        }
        return result.toString();
    }
}
