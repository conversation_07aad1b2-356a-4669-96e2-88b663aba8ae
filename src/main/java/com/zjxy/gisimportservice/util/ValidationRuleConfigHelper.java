package com.zjxy.gisimportservice.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 验证规则配置辅助工具类
 * 用于创建和管理字段验证规则的JSON配置
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-29
 */
@Slf4j
public class ValidationRuleConfigHelper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 创建字段验证规则配置
     */
    public static Map<String, Object> createFieldRule(String fieldName, String checkType, 
                                                     boolean checkedSX, Map<String, Object> additionalConfig) {
        Map<String, Object> rule = new HashMap<>();
        rule.put("fieldName", fieldName);
        rule.put("checkType", checkType);
        rule.put("checkedSX", checkedSX);
        
        if (additionalConfig != null) {
            rule.putAll(additionalConfig);
        }
        
        return rule;
    }

    /**
     * 创建非空验证规则
     */
    public static Map<String, Object> createEmptyValueRule(String fieldName, boolean enabled) {
        return createFieldRule(fieldName, "emptyValue", enabled, null);
    }

    /**
     * 创建枚举值验证规则
     */
    public static Map<String, Object> createEnumsRule(String fieldName, boolean enabled, String enumsCheckVal) {
        Map<String, Object> config = new HashMap<>();
        config.put("enumsCheckVal", enumsCheckVal);
        return createFieldRule(fieldName, "enums", enabled, config);
    }

    /**
     * 创建数值范围验证规则
     */
    public static Map<String, Object> createScaleValueRule(String fieldName, boolean enabled, 
                                                          Double min, Double max) {
        Map<String, Object> config = new HashMap<>();
        if (min != null) {
            config.put("scaleValueMin", min);
        }
        if (max != null) {
            config.put("scaleValueMax", max);
        }
        return createFieldRule(fieldName, "scaleValue", enabled, config);
    }

    /**
     * 创建唯一值验证规则
     */
    public static Map<String, Object> createOnlyValueRule(String fieldName, boolean enabled) {
        return createFieldRule(fieldName, "onlyValue", enabled, null);
    }

    /**
     * 创建模板875的默认验证规则配置
     */
    public static List<Map<String, Object>> createTemplate875DefaultRules() {
        List<Map<String, Object>> rules = new ArrayList<>();

        // 管线编号 - 非空验证 + 枚举值验证
        rules.add(createEnumsRule("gxbh", true, "5200,5300,5400"));

        // 原管线编码 - 非空验证
        rules.add(createEmptyValueRule("ygxbm", false)); // 暂时禁用

        // 关键字 - 非空验证
        rules.add(createEmptyValueRule("gjz", false)); // 暂时禁用

        // 起点物探点号 - 非空验证
        rules.add(createEmptyValueRule("qdwtdh", true));

        // 终点物探点号 - 非空验证
        rules.add(createEmptyValueRule("zdwtdh", true));

        // 起点横坐标 - 数值范围验证
        rules.add(createScaleValueRule("qdhzb", true, 384095.61, 571661.61));

        // 起点纵坐标 - 数值范围验证
        rules.add(createScaleValueRule("qdzzb", true, 2977083.96, 3176239.57));

        // 终点横坐标 - 数值范围验证
        rules.add(createScaleValueRule("zdhzb", true, 384095.61, 571661.61));

        // 终点纵坐标 - 数值范围验证
        rules.add(createScaleValueRule("zdzzb", true, 2977083.96, 3176239.57));

        return rules;
    }

    /**
     * 创建基础验证规则配置（适用于其他模板）
     */
    public static List<Map<String, Object>> createBasicValidationRules() {
        List<Map<String, Object>> rules = new ArrayList<>();

        // 基础的非空验证规则
        rules.add(createEmptyValueRule("name", true));
        rules.add(createEmptyValueRule("code", true));

        return rules;
    }

    /**
     * 将验证规则列表转换为JSON字符串
     */
    public static String rulesToJson(List<Map<String, Object>> rules) {
        try {
            return objectMapper.writeValueAsString(rules);
        } catch (JsonProcessingException e) {
            log.error("转换验证规则为JSON失败", e);
            return "[]";
        }
    }

    /**
     * 从JSON字符串解析验证规则列表
     */
    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> rulesFromJson(String json) {
        try {
            return objectMapper.readValue(json, List.class);
        } catch (Exception e) {
            log.error("从JSON解析验证规则失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 打印验证规则配置（用于调试）
     */
    public static void printRules(List<Map<String, Object>> rules) {
        log.info("=== 验证规则配置 ===");
        for (int i = 0; i < rules.size(); i++) {
            Map<String, Object> rule = rules.get(i);
            log.info("规则 {}: {}", i + 1, rule);
        }
        log.info("=== 配置结束 ===");
    }

    /**
     * 验证规则配置的完整性
     */
    public static boolean validateRuleConfig(Map<String, Object> rule) {
        if (rule == null) {
            return false;
        }
        
        String fieldName = (String) rule.get("fieldName");
        String checkType = (String) rule.get("checkType");
        Boolean checkedSX = (Boolean) rule.get("checkedSX");
        
        if (fieldName == null || fieldName.trim().isEmpty()) {
            log.warn("验证规则缺少fieldName: {}", rule);
            return false;
        }
        
        if (checkType == null || checkType.trim().isEmpty()) {
            log.warn("验证规则缺少checkType: {}", rule);
            return false;
        }
        
        if (checkedSX == null) {
            log.warn("验证规则缺少checkedSX: {}", rule);
            return false;
        }
        
        // 检查特定验证类型的必需配置
        switch (checkType) {
            case "enums":
                String enumsCheckVal = (String) rule.get("enumsCheckVal");
                if (checkedSX && (enumsCheckVal == null || enumsCheckVal.trim().isEmpty())) {
                    log.warn("枚举验证规则缺少enumsCheckVal: {}", rule);
                    return false;
                }
                break;
            case "scaleValue":
                if (checkedSX) {
                    Object min = rule.get("scaleValueMin");
                    Object max = rule.get("scaleValueMax");
                    if (min == null && max == null) {
                        log.warn("数值范围验证规则缺少scaleValueMin或scaleValueMax: {}", rule);
                        return false;
                    }
                }
                break;
        }
        
        return true;
    }

    /**
     * 获取启用的验证规则数量
     */
    public static int getEnabledRuleCount(List<Map<String, Object>> rules) {
        if (rules == null) {
            return 0;
        }
        
        return (int) rules.stream()
                .filter(rule -> Boolean.TRUE.equals(rule.get("checkedSX")))
                .count();
    }
}
