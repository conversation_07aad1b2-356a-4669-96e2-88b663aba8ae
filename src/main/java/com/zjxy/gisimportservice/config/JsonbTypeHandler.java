package com.zjxy.gisimportservice.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PostgreSQL JSONB 类型处理器
 * 用于处理 PostgreSQL 数据库中的 jsonb 字段
 */
@Slf4j
@MappedTypes({String.class})
@MappedJdbcTypes({JdbcType.OTHER})
public class JsonbTypeHandler extends BaseTypeHandler<String> {

    private static final ObjectMapper objectMapper = createObjectMapper();

    /**
     * 创建配置好的 ObjectMapper
     */
    private static ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 配置处理循环引用
        mapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        // 添加几何对象混入
        mapper.addMixIn(com.vividsolutions.jts.geom.Geometry.class, GeometryMixin.class);

        return mapper;
    }

    /**
     * 几何对象混入类，忽略导致循环引用的属性
     */
    @com.fasterxml.jackson.annotation.JsonIgnoreProperties({
        "envelope",
        "envelopeInternal",
        "factory",
        "precisionModel",
        "userData",
        "userDataMap"
    })
    public static abstract class GeometryMixin {
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 创建 PGobject 来处理 jsonb 类型
            PGobject jsonObject = new PGobject();
            jsonObject.setType("jsonb");

            if (parameter == null || parameter.trim().isEmpty()) {
                jsonObject.setValue(null);
            } else {
                // 验证 JSON 格式
                try {
                    objectMapper.readTree(parameter);
                    jsonObject.setValue(parameter);
                } catch (JsonProcessingException e) {
                    log.warn("Invalid JSON format, treating as string: {}", parameter);
                    // 如果不是有效的 JSON，将其包装为 JSON 字符串
                    jsonObject.setValue("\"" + parameter.replace("\"", "\\\"") + "\"");
                }
            }

            ps.setObject(i, jsonObject);

        } catch (Exception e) {
            log.error("Error setting jsonb parameter: {}", e.getMessage(), e);
            throw new SQLException("Error setting jsonb parameter", e);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        try {
            Object object = rs.getObject(columnName);
            if (object == null) {
                return null;
            }
            return object.toString();
        } catch (Exception e) {
            log.error("Error getting jsonb result by column name: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        try {
            Object object = rs.getObject(columnIndex);
            if (object == null) {
                return null;
            }
            return object.toString();
        } catch (Exception e) {
            log.error("Error getting jsonb result by column index: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        try {
            Object object = cs.getObject(columnIndex);
            if (object == null) {
                return null;
            }
            return object.toString();
        } catch (Exception e) {
            log.error("Error getting jsonb result from callable statement: {}", e.getMessage(), e);
            return null;
        }
    }
}
