package com.zjxy.gisimportservice.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Excel导入配置类
 *
 * 从配置文件中读取Excel导入相关的配置参数
 *
 * <AUTHOR> Data Import System
 * @since 2025-07-28
 */
@Data
@Component
@ConfigurationProperties(prefix = "gis.import.excel")
public class ExcelImportConfig {

    /**
     * Excel导入功能开关
     */
    private boolean enabled = true;

    /**
     * 默认批次大小
     */
    private int defaultBatchSize = 1000;

    /**
     * 最大文件大小（MB）
     */
    private int maxFileSize = 100;

    /**
     * 支持的文件格式
     */
    private List<String> supportedFormats;

    /**
     * 临时文件存储路径
     */
    private String tempPath;

    /**
     * 数据处理配置
     */
    private ProcessingConfig processing = new ProcessingConfig();

    /**
     * Excel验证配置
     */
    private ValidationConfig validation = new ValidationConfig();

    /**
     * 数据处理配置内部类
     */
    @Data
    public static class ProcessingConfig {
        /**
         * 是否启用异步处理
         */
        private boolean asyncEnabled = true;

        /**
         * 线程池大小
         */
        private int threadPoolSize = 4;

        /**
         * 队列大小
         */
        private int queueSize = 1000;
    }

    /**
     * Excel验证配置内部类
     */
    @Data
    public static class ValidationConfig {
        /**
         * 检查表头格式
         */
        private boolean checkHeaderFormat = true;

        /**
         * 检查数据一致性
         */
        private boolean checkDataConsistency = true;

        /**
         * Excel最大错误率
         */
        private double maxErrorRate = 5.0;

        /**
         * 是否严格模式
         */
        private boolean strictMode = false;
    }

    /**
     * 验证文件格式是否支持
     *
     * @param fileName 文件名
     * @return 是否支持
     */
    public boolean isSupportedFormat(String fileName) {
        if (fileName == null || supportedFormats == null) {
            return false;
        }

        String lowerFileName = fileName.toLowerCase();
        return supportedFormats.stream()
                .anyMatch(format -> lowerFileName.endsWith(format.toLowerCase()));
    }

    /**
     * 验证文件大小是否在限制范围内
     *
     * @param fileSizeBytes 文件大小（字节）
     * @return 是否在限制范围内
     */
    public boolean isFileSizeValid(long fileSizeBytes) {
        long maxSizeBytes = maxFileSize * 1024L * 1024L; // 转换为字节
        return fileSizeBytes <= maxSizeBytes;
    }

    /**
     * 获取最大文件大小（字节）
     *
     * @return 最大文件大小（字节）
     */
    public long getMaxFileSizeBytes() {
        return maxFileSize * 1024L * 1024L;
    }
}
