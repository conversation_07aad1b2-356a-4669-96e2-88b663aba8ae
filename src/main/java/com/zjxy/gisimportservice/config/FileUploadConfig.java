package com.zjxy.gisimportservice.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件上传配置类
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 临时文件存储目录
     */
    private String tempDir = "D:/xinyu_shixi/temp/gis-uploads";

    /**
     * 最大文件大小
     */
    private String maxFileSize = "500MB";

    /**
     * 最大请求大小
     */
    private String maxRequestSize = "500MB";

    /**
     * 文件保留时间（小时）
     */
    private int retentionHours = 24;

    /**
     * 是否启用自动清理
     */
    private boolean autoCleanupEnabled = true;

    /**
     * 清理任务执行间隔（小时）
     */
    private int cleanupIntervalHours = 6;

    /**
     * 初始化配置
     */
    @PostConstruct
    public void init() {
        try {
            // 确保上传目录存在
            Path uploadPath = Paths.get(tempDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                log.info("创建文件上传目录: {}", uploadPath.toAbsolutePath());
            }

            // 检查目录权限
            File uploadDir = uploadPath.toFile();
            if (!uploadDir.canWrite()) {
                log.warn("上传目录没有写权限: {}", uploadPath.toAbsolutePath());
            }

            log.info("文件上传配置初始化完成:");
            log.info("  上传目录: {}", uploadPath.toAbsolutePath());
            log.info("  最大文件大小: {}", maxFileSize);
            log.info("  文件保留时间: {} 小时", retentionHours);
            log.info("  自动清理: {}", autoCleanupEnabled ? "启用" : "禁用");

        } catch (Exception e) {
            log.error("文件上传配置初始化失败", e);
            throw new RuntimeException("文件上传配置初始化失败", e);
        }
    }

    /**
     * 获取绝对路径
     */
    public String getAbsoluteTempDir() {
        return Paths.get(tempDir).toAbsolutePath().toString();
    }

    /**
     * 检查目录是否可用
     */
    public boolean isDirectoryAvailable() {
        try {
            Path path = Paths.get(tempDir);
            return Files.exists(path) && Files.isDirectory(path) && Files.isWritable(path);
        } catch (Exception e) {
            log.error("检查目录可用性失败: {}", tempDir, e);
            return false;
        }
    }
}
