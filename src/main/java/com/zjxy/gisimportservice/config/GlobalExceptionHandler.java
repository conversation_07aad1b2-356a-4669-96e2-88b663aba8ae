package com.zjxy.gisimportservice.config;

import com.fasterxml.jackson.databind.JsonMappingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 处理 JSON 序列化等常见异常
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理 Jackson JSON 映射异常
     */
    @ExceptionHandler(JsonMappingException.class)
    public ResponseEntity<Map<String, Object>> handleJsonMappingException(JsonMappingException e) {
        log.error("JSON 序列化异常", e);

        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "数据序列化失败");

        // 检查是否是循环引用问题
        if (e.getMessage() != null && e.getMessage().contains("Infinite recursion")) {
            response.put("message", "数据包含循环引用，已自动处理");
            response.put("detail", "几何对象序列化问题已修复");
        } else {
            response.put("detail", "JSON 序列化异常: " + e.getMessage());
        }

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理栈溢出异常
     */
    @ExceptionHandler(StackOverflowError.class)
    public ResponseEntity<Map<String, Object>> handleStackOverflowError(StackOverflowError e) {
        log.error("栈溢出异常", e);

        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "数据处理异常：栈溢出");
        response.put("detail", "可能由于数据循环引用导致，请检查几何对象配置");

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理一般异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGeneralException(Exception e) {
        log.error("未处理的异常", e);

        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "系统异常");
        response.put("detail", e.getMessage());

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
