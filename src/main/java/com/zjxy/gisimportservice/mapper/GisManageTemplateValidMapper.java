package com.zjxy.gisimportservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zjxy.gisimportservice.entity.GisManageTemplateValid;
import org.apache.ibatis.annotations.*;

import java.util.Map;

/**
 * GIS模板验证配置Mapper接口
 */
@Mapper
public interface GisManageTemplateValidMapper extends BaseMapper<GisManageTemplateValid> {

    /**
     * 根据UID查询验证配置 - 使用自定义ResultMap
     */
    @Results(id = "validConfigResultMap", value = {
            @Result(column = "id", property = "id"),
            @Result(column = "uid", property = "uid"),
            @Result(column = "table_name", property = "tableName"),
            @Result(column = "datasource_name", property = "datasourceName"),
            @Result(column = "data_base", property = "dataBase"),
            @Result(column = "data_base_mode", property = "dataBaseMode"),
            @Result(column = "data_base_table", property = "dataBaseTable"),
            @Result(column = "template_id", property = "templateId"),
            @Result(column = "tp", property = "tp", typeHandler = com.zjxy.gisimportservice.handler.ListStringToJsonHandler.class),
            @Result(column = "kj", property = "kj", typeHandler = com.zjxy.gisimportservice.handler.ListStringToJsonHandler.class),
            @Result(column = "sx", property = "sx", typeHandler = com.zjxy.gisimportservice.handler.ListMapToJsonHandler.class),
            @Result(column = "map", property = "map", typeHandler = com.zjxy.gisimportservice.handler.ListMapToJsonHandler.class),
            @Result(column = "kj_condition", property = "kjCondition", typeHandler = com.zjxy.gisimportservice.handler.ListMapToJsonHandler.class)
    })
    @Select("SELECT id, uid, table_name, datasource_name, data_base, data_base_mode, data_base_table, " +
            "template_id, tp::text as tp, kj::text as kj, sx::text as sx, map::text as map, kj_condition " +
            "FROM gis_manage_template_valid WHERE uid = #{uid} LIMIT 1")
    GisManageTemplateValid getByUid(@Param("uid") String uid);

    /**
     * 根据模板ID查询验证配置（通过关联gis_manage_template表）
     * 注意：这个方法需要先从gis_manage_template表获取uid，然后查询验证配置
     */
    @ResultMap("validConfigResultMap")
    @Select("SELECT gmtv.id, gmtv.uid, gmtv.table_name, gmtv.datasource_name, gmtv.data_base, " +
            "gmtv.data_base_mode, gmtv.data_base_table, gmtv.template_id, " +
            "gmtv.tp::text as tp, gmtv.kj::text as kj, gmtv.sx::text as sx, gmtv.map::text as map, gmtv.kj_condition " +
            "FROM gis_manage_template_valid gmtv " +
            "INNER JOIN gis_manage_template gmt ON gmtv.uid = gmt.uid " +
            "WHERE gmt.id = #{templateId} LIMIT 1")
    GisManageTemplateValid getByTemplateId(@Param("templateId") Integer templateId);

    /**
     * 调试方法：查看数据库中JSON字段的原始数据
     */
    @Select("SELECT id, uid, tp, kj, sx, map, " +
            "pg_typeof(tp) as tp_type, pg_typeof(kj) as kj_type, " +
            "pg_typeof(sx) as sx_type, pg_typeof(map) as map_type " +
            "FROM gis_manage_template_valid WHERE uid = #{uid} LIMIT 1")
    Map<String, Object> getRawDataByUid(@Param("uid") String uid);

    /**
     * 简化测试查询：只查询一个JSON字段
     */
    @Select("SELECT id, uid, sx FROM gis_manage_template_valid WHERE uid = #{uid} LIMIT 1")
    GisManageTemplateValid getSimpleByUid(@Param("uid") String uid);
}
