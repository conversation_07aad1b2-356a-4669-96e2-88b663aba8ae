package com.zjxy.gisimportservice.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjxy.gisimportservice.entity.GisImportTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * GIS数据导入任务Mapper接口
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
@Mapper
@DS("slave")
public interface GisImportTaskMapper extends BaseMapper<GisImportTask> {

    /**
     * 根据模板ID查询导入任务列表
     *
     * @param templateId 模板ID
     * @return 导入任务列表
     */
    @Select("SELECT * FROM gis_import_task WHERE template_id = #{templateId} ORDER BY import_time DESC")
    List<GisImportTask> findByTemplateId(@Param("templateId") Integer templateId);

    /**
     * 根据数据状态查询导入任务列表
     *
     * @param dataStatus 数据状态
     * @return 导入任务列表
     */
    @Select("SELECT * FROM gis_import_task WHERE data_status = #{dataStatus} ORDER BY import_time DESC")
    List<GisImportTask> findByDataStatus(@Param("dataStatus") Integer dataStatus);

    /**
     * 分页查询导入任务列表
     *
     * @param page 分页参数
     * @param dataStatus 数据状态（可选）
     * @param importFormat 导入格式（可选）
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT t.*, m.name_zh as templateName " +
            "FROM gis_import_task t " +
            "LEFT JOIN gis_manage_template m ON t.template_id = m.id " +
            "<where> " +
            "  <if test='dataStatus != null'>AND t.data_status = #{dataStatus}</if> " +
            "  <if test='importFormat != null'>AND t.import_format = #{importFormat}</if> " +
            "</where> " +
            "ORDER BY t.import_time DESC" +
            "</script>")
    IPage<GisImportTask> pageQuery(Page<GisImportTask> page,
                                  @Param("dataStatus") Integer dataStatus,
                                  @Param("importFormat") Integer importFormat);

    /**
     * 更新任务状态
     *
     * @param id 任务ID
     * @param dataStatus 数据状态
     * @return 影响行数
     */
    @Update("UPDATE gis_import_task SET data_status = #{dataStatus}, updated_time = NOW() WHERE id = #{id}")
    int updateTaskStatus(@Param("id") Long id, @Param("dataStatus") Integer dataStatus);

    /**
     * 更新处理进度
     *
     * @param id 任务ID
     * @param processedCount 已处理记录数
     * @param errorCount 错误记录数
     * @return 影响行数
     */
    @Update("UPDATE gis_import_task SET processed_count = #{processedCount}, error_count = #{errorCount}, " +
            "updated_time = NOW() WHERE id = #{id}")
    int updateProgress(@Param("id") Long id,
                      @Param("processedCount") Integer processedCount,
                      @Param("errorCount") Integer errorCount);

    /**
     * 更新任务完成信息
     *
     * @param id 任务ID
     * @param dataStatus 数据状态
     * @param processedCount 已处理记录数
     * @param errorCount 错误记录数
     * @param errorMessage 错误信息
     * @return 影响行数
     */
    @Update("UPDATE gis_import_task SET data_status = #{dataStatus}, " +
            "processed_count = #{processedCount}, error_count = #{errorCount}, " +
            "error_message = #{errorMessage}, end_time = NOW(), " +
            "duration_ms = TIMESTAMPDIFF(MICROSECOND, start_time, NOW()) / 1000, " +
            "success_rate = IF(record_count > 0, ((record_count - #{errorCount}) * 100.0 / record_count), 0), " +
            "updated_time = NOW() WHERE id = #{id}")
    int updateTaskCompletion(@Param("id") Long id,
                            @Param("dataStatus") Integer dataStatus,
                            @Param("processedCount") Integer processedCount,
                            @Param("errorCount") Integer errorCount,
                            @Param("errorMessage") String errorMessage);

    /**
     * 标记任务开始处理
     *
     * @param id 任务ID
     * @param recordCount 预计处理记录数
     * @return 影响行数
     */
    @Update("UPDATE gis_import_task SET data_status = 1, start_time = NOW(), " +
            "record_count = #{recordCount}, updated_time = NOW() WHERE id = #{id}")
    int markTaskStarted(@Param("id") Long id, @Param("recordCount") Integer recordCount);

    /**
     * 查询未完成的任务数量
     *
     * @return 未完成任务数量
     */
    @Select("SELECT COUNT(*) FROM gis_import_task WHERE data_status < 2")
    int countPendingTasks();
}
