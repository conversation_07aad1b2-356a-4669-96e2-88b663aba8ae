-- Excel坐标映射强制迁移到列名配置的SQL脚本
-- ⚠️  重要：这是一个破坏性变更，系统不再支持位置配置！
-- ⚠️  执行前请备份数据库！所有模板必须迁移到列名格式！

-- =====================================================
-- 1. 查看当前的坐标映射配置
-- =====================================================

-- 查看所有Excel模板的坐标映射配置
SELECT
    id,
    template_name,
    template_type,
    type,
    sheet_name,
    point_map,
    line_map
FROM gis_manage_template
WHERE template_type = 'excel'
  AND (type = 2 OR type = 3)
ORDER BY id;

-- =====================================================
-- 2. 线表坐标映射迁移示例
-- =====================================================

-- 示例：将模板ID 877的line_map从位置配置改为列名配置
-- 假设原配置：{"x": 5, "y": 6, "x1": 9, "y1": 10, "qdbh": "", "zdbh": "", "targetPointTable": "", "pointTableBh": ""}
-- 目标配置：{"x": "起点横坐标", "y": "起点纵坐标", "x1": "终点横坐标", "y1": "终点纵坐标", "qdbh": "", "zdbh": "", "targetPointTable": "", "pointTableBh": ""}

UPDATE gis_manage_template
SET line_map = JSON_SET(
    COALESCE(line_map, '{}'),
    '$.x', '起点横坐标',
    '$.y', '起点纵坐标',
    '$.x1', '终点横坐标',
    '$.y1', '终点纵坐标'
)
WHERE id = 877
  AND template_type = 'excel'
  AND type = 3;

-- =====================================================
-- 3. 点表坐标映射迁移示例
-- =====================================================

-- 示例：将点表模板的point_map从位置配置改为列名配置
-- 假设原配置：{"x": 3, "y": 4}
-- 目标配置：{"x": "X坐标", "y": "Y坐标"}

UPDATE gis_manage_template
SET point_map = JSON_SET(
    COALESCE(point_map, '{}'),
    '$.x', 'X坐标',
    '$.y', 'Y坐标'
)
WHERE template_type = 'excel'
  AND type = 2
  AND id IN (
    -- 在这里指定需要更新的点表模板ID
    -- 例如：875, 876
  );

-- =====================================================
-- 4. 批量迁移脚本（谨慎使用）
-- =====================================================

-- 注意：以下脚本会批量更新所有Excel模板的坐标配置
-- 请根据实际的Excel表头列名修改配置值
-- 执行前请确保已经备份数据库！

-- 批量更新所有线表模板
-- UPDATE gis_manage_template
-- SET line_map = JSON_SET(
--     COALESCE(line_map, '{}'),
--     '$.x', '起点横坐标',
--     '$.y', '起点纵坐标',
--     '$.x1', '终点横坐标',
--     '$.y1', '终点纵坐标'
-- )
-- WHERE template_type = 'excel'
--   AND type = 3
--   AND line_map IS NOT NULL;

-- 批量更新所有点表模板
-- UPDATE gis_manage_template
-- SET point_map = JSON_SET(
--     COALESCE(point_map, '{}'),
--     '$.x', 'X坐标',
--     '$.y', 'Y坐标'
-- )
-- WHERE template_type = 'excel'
--   AND type = 2
--   AND point_map IS NOT NULL;

-- =====================================================
-- 5. 验证迁移结果
-- =====================================================

-- 查看迁移后的配置
SELECT
    id,
    template_name,
    type,
    CASE
        WHEN type = 2 THEN point_map
        WHEN type = 3 THEN line_map
        ELSE NULL
    END as coordinate_mapping
FROM gis_manage_template
WHERE template_type = 'excel'
  AND (type = 2 OR type = 3)
ORDER BY id;

-- ⚠️  关键检查：识别所有使用数字位置配置的模板（必须迁移）
-- 这些模板在新系统中将无法工作！
SELECT
    id,
    template_name,
    type,
    line_map,
    point_map,
    '⚠️ 必须迁移' as migration_status
FROM gis_manage_template
WHERE template_type = 'excel'
  AND (
    (type = 3 AND (
        JSON_TYPE(JSON_EXTRACT(line_map, '$.x')) = 'INTEGER' OR
        JSON_TYPE(JSON_EXTRACT(line_map, '$.y')) = 'INTEGER' OR
        JSON_TYPE(JSON_EXTRACT(line_map, '$.x1')) = 'INTEGER' OR
        JSON_TYPE(JSON_EXTRACT(line_map, '$.y1')) = 'INTEGER'
    )) OR
    (type = 2 AND (
        JSON_TYPE(JSON_EXTRACT(point_map, '$.x')) = 'INTEGER' OR
        JSON_TYPE(JSON_EXTRACT(point_map, '$.y')) = 'INTEGER'
    ))
  );

-- 如果上述查询返回任何结果，必须先迁移这些模板才能升级系统！

-- =====================================================
-- 6. 常用的列名配置模板
-- =====================================================

-- 常见的线表列名配置
/*
燃气管线：
{
    "x": "起点横坐标",
    "y": "起点纵坐标",
    "x1": "终点横坐标",
    "y1": "终点纵坐标",
    "qdbh": "",
    "zdbh": "",
    "targetPointTable": "",
    "pointTableBh": ""
}

给水管线：
{
    "x": "起点X",
    "y": "起点Y",
    "x1": "终点X",
    "y1": "终点Y",
    "qdbh": "",
    "zdbh": "",
    "targetPointTable": "",
    "pointTableBh": ""
}

电力线路：
{
    "x": "始端经度",
    "y": "始端纬度",
    "x1": "末端经度",
    "y1": "末端纬度",
    "qdbh": "",
    "zdbh": "",
    "targetPointTable": "",
    "pointTableBh": ""
}
*/

-- 常见的点表列名配置
/*
设备点位：
{
    "x": "经度",
    "y": "纬度"
}

监测点：
{
    "x": "X坐标",
    "y": "Y坐标"
}

井盖点位：
{
    "x": "横坐标",
    "y": "纵坐标"
}
*/

-- =====================================================
-- 7. 回滚脚本（紧急情况使用）
-- =====================================================

-- 如果需要回滚到位置配置，可以使用以下脚本
-- 注意：这会丢失列名配置，请谨慎使用

-- 回滚线表配置到位置索引
-- UPDATE gis_manage_template
-- SET line_map = JSON_SET(
--     line_map,
--     '$.x', 5,
--     '$.y', 6,
--     '$.x1', 9,
--     '$.y1', 10
-- )
-- WHERE id = 877;

-- 回滚点表配置到位置索引
-- UPDATE gis_manage_template
-- SET point_map = JSON_SET(
--     point_map,
--     '$.x', 3,
--     '$.y', 4
-- )
-- WHERE id = 876;

-- =====================================================
-- 8. 数据完整性检查
-- =====================================================

-- 检查所有Excel模板是否都有有效的坐标配置
SELECT
    id,
    template_name,
    type,
    CASE
        WHEN type = 2 AND (point_map IS NULL OR point_map = '{}') THEN 'MISSING_POINT_MAP'
        WHEN type = 3 AND (line_map IS NULL OR line_map = '{}') THEN 'MISSING_LINE_MAP'
        WHEN type = 2 AND (
            JSON_EXTRACT(point_map, '$.x') IS NULL OR
            JSON_EXTRACT(point_map, '$.y') IS NULL
        ) THEN 'INCOMPLETE_POINT_MAP'
        WHEN type = 3 AND (
            JSON_EXTRACT(line_map, '$.x') IS NULL OR
            JSON_EXTRACT(line_map, '$.y') IS NULL OR
            JSON_EXTRACT(line_map, '$.x1') IS NULL OR
            JSON_EXTRACT(line_map, '$.y1') IS NULL
        ) THEN 'INCOMPLETE_LINE_MAP'
        ELSE 'OK'
    END as status
FROM gis_manage_template
WHERE template_type = 'excel'
  AND (type = 2 OR type = 3)
HAVING status != 'OK'
ORDER BY id;

-- =====================================================
-- 执行建议
-- =====================================================

/*
1. 执行前备份：
   mysqldump -u username -p database_name gis_manage_template > backup_templates.sql

2. 分步执行：
   - 先执行查看脚本，了解当前配置
   - 选择一个测试模板进行单个更新
   - 验证更新结果
   - 逐步更新其他模板

3. 测试验证：
   - 更新后使用Excel导入功能测试
   - 确认坐标映射工作正常
   - 检查坐标转换功能

4. 监控日志：
   - 观察应用日志中的坐标映射信息
   - 确认没有列名找不到的错误

5. 回滚准备：
   - 保留原始配置的备份
   - 准备回滚脚本以防万一
*/
