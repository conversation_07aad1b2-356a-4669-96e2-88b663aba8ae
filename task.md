# GIS导入服务基于data_status的"验证-导入"两阶段业务流程重构任务清单

## 业务背景

**核心理念**：基于GIS导入任务表的data_status字段，实现验证和导入两个独立的业务阶段，避免重复工作，提高系统效率

### data_status状态定义：
- **data_status=1**：数据未检查（初始状态）
- **data_status=0**：数据未导入（验证完成）
- **data_status=2**：数据已导入（导入完成）

### 业务流程设计：
```
创建任务 → data_status=1(数据未检查)
    ↓ 点击验证(target="valid")
验证完成 → data_status=0(数据未导入)
    ↓ 点击导入(target="import")
导入完成 → data_status=2(数据已导入)
```

### 前端交互逻辑：
- **data_status=1时**：显示"验证"按钮，禁用"导入"按钮
- **data_status=0时**：显示"重新验证"和"导入"按钮
- **data_status=2时**：显示"重新验证"和"重新导入"按钮

### 性能目标：
- **验证模式**：专注验证，不执行数据库操作，比原有快50%
- **导入模式**：跳过重复验证，直接导入，比原有快30-50%
- **状态管理**：基于data_status的精确状态控制

### UI控制逻辑：
1. **data_status=1**：只能点击「验证」，「导入」按钮禁用
2. **data_status=0**：「验证」和「导入」按钮都可用
3. **data_status=2**：所有操作按钮禁用，只能查看结果

### 性能目标：
- 验证模式：专注验证，不执行数据库操作
- 导入模式：跳过重复验证，性能提升30-50%

## 阶段1：基于data_status的状态管理实现（0.5天）

### 任务1.1：实现data_status状态管理服务
**优先级**：🔴 高
**目标**：基于data_status字段实现验证-导入两阶段状态控制

#### 创建状态管理服务：
```java
// 1. 创建DataStatusManager服务
@Service
public class DataStatusManager {

    @Autowired
    private GisImportTaskService gisImportTaskService;

    /**
     * 更新任务为验证完成状态
     * data_status: 1 → 0
     */
    public void updateToValidated(Long taskId, ValidationResult result) {
        GisImportTask task = gisImportTaskService.getById(taskId);
        if (task == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }

        // 检查状态转换合法性
        if (task.getDataStatus() != 1) {
            throw new IllegalStateException("任务状态错误，当前状态: " + task.getDataStatus() + "，期望状态: 1");
        }

        // 更新状态
        task.setDataStatus(0); // 数据未导入
        task.setValidationTime(new Date());
        task.setValidationResult(JSON.toJSONString(result));

        // 记录状态变更日志
        String changeLog = String.format("验证完成: %s -> %s, 时间: %s",
                "数据未检查", "数据未导入", new Date());
        task.setStatusChangeLog(appendLog(task.getStatusChangeLog(), changeLog));

        gisImportTaskService.updateById(task);
        log.info("任务状态更新: {} -> data_status=0(数据未导入)", taskId);
    }

    /**
     * 更新任务为导入完成状态
     * data_status: 0 → 2
     */
    public void updateToImported(Long taskId, ValidationResult result) {
        GisImportTask task = gisImportTaskService.getById(taskId);
        if (task == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }

        // 检查状态转换合法性
        if (task.getDataStatus() != 0) {
            throw new IllegalStateException("任务状态错误，当前状态: " + task.getDataStatus() + "，期望状态: 0");
        }

        // 更新状态
        task.setDataStatus(2); // 数据已导入
        task.setImportTime(new Date());
        task.setImportResult(JSON.toJSONString(result));

        // 记录状态变更日志
        String changeLog = String.format("导入完成: %s -> %s, 时间: %s",
                "数据未导入", "数据已导入", new Date());
        task.setStatusChangeLog(appendLog(task.getStatusChangeLog(), changeLog));

        gisImportTaskService.updateById(task);
        log.info("任务状态更新: {} -> data_status=2(数据已导入)", taskId);
    }

    /**
     * 检查任务是否可以执行验证
     */
    public boolean canValidate(Long taskId) {
        GisImportTask task = gisImportTaskService.getById(taskId);
        return task != null && task.getDataStatus() == 1;
    }

    /**
     * 检查任务是否可以执行导入
     */
    public boolean canImport(Long taskId) {
        GisImportTask task = gisImportTaskService.getById(taskId);
        return task != null && task.getDataStatus() == 0;
    }

    /**
     * 获取任务状态描述
     */
    public String getStatusDescription(Integer dataStatus) {
        switch (dataStatus) {
            case 1: return "数据未检查";
            case 0: return "数据未导入";
            case 2: return "数据已导入";
            default: return "未知状态";
        }
    }

    /**
     * 检查任务是否已验证通过
     */
    public boolean isValidated(Long taskId) {
        GisImportTask task = gisImportTaskService.getById(taskId);
        return task != null && task.getDataStatus() == 0;
    }

    /**
     * 更新任务为验证失败状态
     */
    public void updateToValidationFailed(Long taskId, ValidationResult result) {
        GisImportTask task = gisImportTaskService.getById(taskId);
        if (task == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }

        // 验证失败，保持data_status=1，但记录失败信息
        task.setValidationTime(new Date());
        task.setValidationResult(JSON.toJSONString(result));
        task.setErrorMessage("验证失败: " + result.getMessage());

        String changeLog = String.format("验证失败: 状态保持为数据未检查, 时间: %s", new Date());
        task.setStatusChangeLog(appendLog(task.getStatusChangeLog(), changeLog));

        gisImportTaskService.updateById(task);
        log.warn("任务验证失败 - 任务ID: {}", taskId);
    }

    /**
     * 更新任务为导入失败状态
     */
    public void updateToImportFailed(Long taskId, String errorMessage) {
        GisImportTask task = gisImportTaskService.getById(taskId);
        if (task == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }

        // 导入失败，保持data_status=0，但记录失败信息
        task.setImportTime(new Date());
        task.setErrorMessage("导入失败: " + errorMessage);

        String changeLog = String.format("导入失败: 状态保持为数据未导入, 时间: %s", new Date());
        task.setStatusChangeLog(appendLog(task.getStatusChangeLog(), changeLog));

        gisImportTaskService.updateById(task);
        log.warn("任务导入失败 - 任务ID: {}", taskId);
    }

    private String appendLog(String existingLog, String newLog) {
        if (existingLog == null || existingLog.trim().isEmpty()) {
            return newLog;
        }
        return existingLog + "\n" + newLog;
    }
}
```

#### 添加状态审计字段：
```sql
-- 添加状态变更审计字段（如果不存在）
ALTER TABLE gis_import_task ADD COLUMN IF NOT EXISTS validation_time TIMESTAMP COMMENT '验证完成时间';
ALTER TABLE gis_import_task ADD COLUMN IF NOT EXISTS import_time TIMESTAMP COMMENT '导入完成时间';
ALTER TABLE gis_import_task ADD COLUMN IF NOT EXISTS validation_result TEXT COMMENT '验证结果详情';
ALTER TABLE gis_import_task ADD COLUMN IF NOT EXISTS import_result TEXT COMMENT '导入结果详情';
ALTER TABLE gis_import_task ADD COLUMN IF NOT EXISTS status_change_log TEXT COMMENT '状态变更日志';
ALTER TABLE gis_import_task ADD COLUMN IF NOT EXISTS error_message TEXT COMMENT '错误信息';
```

## 阶段2：验证模式重构（1天）

### 任务2.1：重构验证模式逻辑
**优先级**：🔴 高
**问题**：当前验证模式仍执行数据库插入
**目标**：实现纯验证，不执行任何数据库操作

#### 修改文件：
1. `TemplateBasedShapefileServiceImpl.java`
2. `ExcelImportServiceImpl.java`
3. `DataValidationServiceImpl.java`
4. `GisImportTaskServiceImpl.java`

#### 具体任务：
```java
// 1. 修改TemplateBasedShapefileServiceImpl.validateShapefileData()
@Override
public ValidationResult validateShapefileData(String filePath, GisManageTemplate template,
                                            String target, String createdBy) {
    if ("valid".equals(target)) {
        // 纯验证模式：只验证，不插入数据库，更新任务状态
        return performPureValidation(filePath, template, createdBy);
    } else if ("import".equals(target)) {
        // 导入模式：前提是已验证通过，跳过验证，直接导入
        return performDirectImport(filePath, template, createdBy);
    }
}

// 2. 实现纯验证方法
private ValidationResult performPureValidation(String filePath, GisManageTemplate template, String createdBy) {
    log.info("执行纯验证模式 - 只验证数据，不执入数据库操作");

    try {
        // 根据文件路径查找任务ID
        Long taskId = findTaskIdByFilePath(filePath);
        if (taskId == null) {
            throw new IllegalArgumentException("未找到对应的导入任务: " + filePath);
        }

        // 检查任务状态是否允许验证
        if (!dataStatusManager.canValidate(taskId)) {
            throw new IllegalStateException("任务状态不允许执行验证操作");
        }

        // 调用纯验证服务（不插入数据库）
        ValidationResult result = pureValidationService.validateShapefileOnly(filePath, template);

        // 更新任务状态
        if (result.isPassed()) {
            dataStatusManager.updateToValidated(taskId, result);
            log.info("验证成功，任务状态更新为: data_status=0(数据未导入)");
        } else {
            dataStatusManager.updateToValidationFailed(taskId, result);
            log.warn("验证失败，任务状态保持为: data_status=1(数据未检查)");
        }

        return result;

    } catch (Exception e) {
        log.error("纯验证模式执行失败", e);
        ValidationResult errorResult = new ValidationResult();
        errorResult.setPassed(false);
        errorResult.setMessage("验证失败: " + e.getMessage());
        return errorResult;
    }
}

// 3. 实现直接导入方法
private ValidationResult performDirectImport(String filePath, GisManageTemplate template, String createdBy) {
    log.info("执行导入模式 - 跳过验证，直接导入数据");

    try {
        // 根据文件路径查找任务ID
        Long taskId = findTaskIdByFilePath(filePath);
        if (taskId == null) {
            throw new IllegalArgumentException("未找到对应的导入任务: " + filePath);
        }

        // 检查前置条件：必须已经验证通过
        if (!dataStatusManager.canImport(taskId)) {
            throw new IllegalStateException("任务状态不允许执行导入操作，请先完成验证");
        }

        // 直接执行导入，跳过验证步骤
        ValidationResult result = directImportService.importShapefileDirectly(filePath, template);

        // 更新任务状态
        if (result.isPassed()) {
            dataStatusManager.updateToImported(taskId, result);
            log.info("导入成功，任务状态更新为: data_status=2(数据已导入)");
        } else {
            dataStatusManager.updateToImportFailed(taskId, result.getMessage());
            log.warn("导入失败，任务状态保持为: data_status=0(数据未导入)");
        }

        return result;

    } catch (Exception e) {
        log.error("直接导入模式执行失败", e);
        ValidationResult errorResult = new ValidationResult();
        errorResult.setPassed(false);
        errorResult.setMessage("导入失败: " + e.getMessage());
        return errorResult;
    }
}

// 4. 辅助方法：根据文件路径查找任务ID
private Long findTaskIdByFilePath(String filePath) {
    // 实现根据文件路径查找任务ID的逻辑
    // 这里需要根据实际的业务逻辑来实现
    GisImportTask task = gisImportTaskService.findByFilePath(filePath);
    return task != null ? task.getId() : null;
}
```

### 任务2.2：创建纯验证服务
**优先级**：🔴 高
**目标**：实现专门的验证服务，不执行数据库操作

#### 新增文件：
1. `service/PureValidationService.java`
2. `service/impl/PureValidationServiceImpl.java`
3. `util/CoordinateValidationUtil.java`
4. `service/DirectImportService.java`

#### 具体任务：
```java
// 1. 创建纯验证服务接口
public interface PureValidationService {
    /**
     * 纯验证Shapefile数据（不插入数据库）
     */
    ValidationResult validateShapefileOnly(String filePath, GisManageTemplate template);

    /**
     * 纯验证Excel数据（不插入数据库）
     */
    ValidationResult validateExcelOnly(String filePath, GisManageTemplate template);
}

// 2. 实现纯验证服务
@Service
public class PureValidationServiceImpl implements PureValidationService {

    @Override
    public ValidationResult validateShapefileOnly(String filePath, GisManageTemplate template) {
        log.info("开始纯验证Shapefile - 文件: {}", filePath);

        ValidationResult result = new ValidationResult();

        try {
            // 1. 读取Shapefile数据（不插入数据库）
            List<SimpleFeature> features = readShapefileFeatures(filePath);
            result.setTotalRecords(features.size());

            // 2. 执行各种验证
            List<ValidationResult.ValidationError> allErrors = new ArrayList<>();

            // 2.1 字段映射验证
            allErrors.addAll(validateFieldMapping(features, template));

            // 2.2 数据类型验证
            allErrors.addAll(validateDataTypes(features, template));

            // 2.3 坐标格式验证（不进行实际转换）
            allErrors.addAll(validateCoordinateFormat(features, template));

            // 2.4 业务规则验证
            allErrors.addAll(validateBusinessRules(features, template));

            // 3. 汇总结果
            result.setErrors(allErrors);
            result.setErrorRecords(allErrors.size());
            result.setValidRecords(features.size() - allErrors.size());
            result.setPassed(allErrors.isEmpty());

            log.info("纯验证完成 - 总记录: {}, 有效: {}, 错误: {}",
                    result.getTotalRecords(), result.getValidRecords(), result.getErrorRecords());

        } catch (Exception e) {
            log.error("纯验证失败", e);
            result.setPassed(false);
            result.setMessage("验证失败: " + e.getMessage());
        }

        return result;
    }
}

// 3. 坐标格式验证工具（不进行实际转换）
public class CoordinateValidationUtil {

    public static List<ValidationResult.ValidationError> validateCoordinateFormat(
            List<SimpleFeature> features, GisManageTemplate template) {

        List<ValidationResult.ValidationError> errors = new ArrayList<>();

        for (int i = 0; i < features.size(); i++) {
            SimpleFeature feature = features.get(i);
            Object geometry = feature.getDefaultGeometry();

            if (geometry instanceof com.vividsolutions.jts.geom.Geometry) {
                com.vividsolutions.jts.geom.Geometry geom = (com.vividsolutions.jts.geom.Geometry) geometry;

                // 只验证坐标格式和范围，不进行实际转换
                ValidationResult.ValidationError error = validateGeometryFormat(i, feature.getID(), geom);
                if (error != null) {
                    errors.add(error);
                }
            }
        }

        return errors;
    }

    private static ValidationResult.ValidationError validateGeometryFormat(
            int recordIndex, String featureId, com.vividsolutions.jts.geom.Geometry geometry) {

        // 1. 检查几何有效性
        if (!geometry.isValid()) {
            return createValidationError(recordIndex, featureId, "geometry",
                    "几何对象无效", ValidationResult.ErrorLevel.ERROR);
        }

        // 2. 检查坐标范围（假设是WGS84坐标系）
        Coordinate[] coordinates = geometry.getCoordinates();
        for (Coordinate coord : coordinates) {
            if (!isValidWGS84Coordinate(coord.x, coord.y)) {
                return createValidationError(recordIndex, featureId, "geometry",
                        String.format("坐标超出有效范围: (%.6f, %.6f)", coord.x, coord.y),
                        ValidationResult.ErrorLevel.WARNING);
            }
        }

        return null; // 验证通过
    }

    private static boolean isValidWGS84Coordinate(double x, double y) {
        // WGS84坐标系范围检查
        return x >= -180 && x <= 180 && y >= -90 && y <= 90;
    }
}

// 4. 创建直接导入服务
@Service
public class DirectImportService {

    @Autowired
    private ShapefileReaderServiceImpl shapefileReader;

    @Autowired
    private DynamicDataSourceManager dataSourceManager;

    /**
     * 直接导入Shapefile（跳过验证步骤）
     */
    public ValidationResult importShapefileDirectly(String filePath, GisManageTemplate template) {
        log.info("开始直接导入Shapefile - 跳过验证步骤，文件: {}", filePath);

        ValidationResult result = new ValidationResult();

        try {
            // 设置数据源
            if (template.getDataBase() != null) {
                dataSourceManager.useDataSource(template.getDataBase());
            }

            // 直接调用ShapefileReader的导入方法，跳过验证
            try (FileInputStream fis = new FileInputStream(new File(filePath))) {
                int importedCount = shapefileReader.processShapefileZipWithTemplateDirectly(
                        fis, new File(filePath).getName(), template.getId());

                // 构建导入结果
                result.setTotalRecords(importedCount);
                result.setValidRecords(importedCount);
                result.setErrorRecords(0);
                result.setPassed(true);
                result.setMessage("直接导入成功，导入了 " + importedCount + " 条记录");

                log.info("直接导入完成 - 导入记录: {}", importedCount);

            }

        } catch (Exception e) {
            log.error("直接导入失败", e);
            result.setPassed(false);
            result.setMessage("导入失败: " + e.getMessage());
            result.setTotalRecords(0);
            result.setValidRecords(0);
            result.setErrorRecords(1);
        }

        return result;
    }

    /**
     * 直接导入Excel（跳过验证步骤）
     */
    public ValidationResult importExcelDirectly(String filePath, GisManageTemplate template) {
        log.info("开始直接导入Excel - 跳过验证步骤，文件: {}", filePath);

        ValidationResult result = new ValidationResult();

        try {
            // 设置数据源
            if (template.getDataBase() != null) {
                dataSourceManager.useDataSource(template.getDataBase());
            }

            // 创建导入专用的ExcelDataListener（跳过验证）
            ExcelDataListener importListener = new ExcelDataListener(template, "import", false);
            importListener.setSkipValidation(true); // 跳过验证步骤

            // 直接导入数据
            EasyExcel.read(filePath, importListener)
                    .sheet(template.getSheetName())
                    .headRowNumber(calculateHeadRowNumber(template))
                    .doRead();

            // 获取导入结果
            result = importListener.getImportResult();
            log.info("Excel直接导入完成 - 导入记录: {}", result.getValidRecords());

        } catch (Exception e) {
            log.error("Excel直接导入失败", e);
            result.setPassed(false);
            result.setMessage("导入失败: " + e.getMessage());
        }

        return result;
    }
}
```

## 阶段3：导入模式重构（1天）

### 任务3.1：重构导入模式逻辑
**优先级**：🔴 高
**目标**：导入模式跳过验证，直接执行数据库操作，提升性能30-50%

#### 修改文件：
1. `TemplateBasedShapefileServiceImpl.java`
2. `ExcelImportServiceImpl.java`
3. `ShapefileReaderServiceImpl.java`

#### 具体任务：
```java
// 1. 实现直接导入方法（跳过验证）
private ValidationResult performDirectDataImport(String filePath, GisManageTemplate template, String createdBy) {
    log.info("执行直接导入模式 - 跳过验证步骤，直接导入数据");

    ValidationResult result = new ValidationResult();

    try {
        // 更新任务状态为"导入中"
        updateTaskImportStatus(filePath, "IMPORTING", null);

        // 直接调用导入服务，跳过验证步骤
        Map<String, Object> importResult = directImportService.importShapefileDirectly(filePath, template);

        // 处理导入结果
        boolean success = (Boolean) importResult.getOrDefault("success", false);
        Integer importedCount = (Integer) importResult.getOrDefault("featuresProcessed", 0);

        result.setTotalRecords(importedCount);
        result.setValidRecords(importedCount);
        result.setErrorRecords(0);
        result.setPassed(success);

        if (success) {
            result.setMessage("直接导入成功，导入了 " + importedCount + " 条记录");
            updateTaskImportStatus(filePath, "IMPORTED", result);
        } else {
            result.setMessage("直接导入失败: " + importResult.getOrDefault("message", "未知错误"));
            updateTaskImportStatus(filePath, "IMPORT_FAILED", result);
        }

        log.info("直接导入完成 - 导入记录: {}, 成功: {}", importedCount, success);

    } catch (Exception e) {
        log.error("直接导入失败", e);
        result.setPassed(false);
        result.setMessage("导入失败: " + e.getMessage());
        updateTaskImportStatus(filePath, "IMPORT_FAILED", result);
    }

    return result;
}

// 2. 创建直接导入服务
@Service
public class DirectImportService {

    public Map<String, Object> importShapefileDirectly(String filePath, GisManageTemplate template) {
        log.info("开始直接导入Shapefile - 跳过验证步骤");

        // 设置数据源
        if (template.getDataBase() != null) {
            DynamicDataSourceManager.build().useDataSource(template.getDataBase());
        }

        try (FileInputStream fis = new FileInputStream(new File(filePath))) {
            // 直接调用ShapefileReader的导入方法，跳过验证
            return shapefileReader.processShapefileZipWithTemplateDirectly(fis,
                    new File(filePath).getName(), template.getId());
        } catch (Exception e) {
            log.error("直接导入失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "导入失败: " + e.getMessage());
            errorResult.put("featuresProcessed", 0);
            return errorResult;
        }
    }
}

// 3. 修改ShapefileReaderServiceImpl，添加直接导入方法
public int processShapefileZipWithTemplateDirectly(InputStream zipInputStream, String fileName, Integer templateId) {
    log.info("直接导入模式 - 跳过验证，专注于数据转换和插入");

    // 跳过验证步骤，直接进行：
    // 1. 文件解压和读取
    // 2. 数据转换（包括坐标系转换）
    // 3. 批量插入数据库

    return processShapefileWithHighPerformanceBatching(collection, schema, template, true); // skipValidation=true
}
```

### 任务3.2：Excel验证和导入模式重构
**优先级**：🔴 高
**目标**：Excel同样支持验证-导入两阶段流程

#### 修改文件：
1. `ExcelImportServiceImpl.java`
2. `ExcelDataListener.java`

#### 具体任务：
```java
// 1. 修改ExcelImportServiceImpl，支持两阶段模式
@Override
public ValidationResult validateExcelData(String filePath, GisManageTemplate template, String target) {
    if ("valid".equals(target)) {
        // 纯验证模式
        return performExcelPureValidation(filePath, template);
    } else if ("import".equals(target)) {
        // 直接导入模式
        return performExcelDirectImport(filePath, template);
    }
}

// 2. Excel纯验证方法
private ValidationResult performExcelPureValidation(String filePath, GisManageTemplate template) {
    log.info("Excel纯验证模式 - 只验证，不插入数据库");

    // 创建验证专用的ExcelDataListener
    ExcelDataListener validationListener = new ExcelDataListener(template, "valid", true); // validationOnly=true

    // 读取Excel但不插入数据库
    EasyExcel.read(filePath, validationListener)
            .sheet(template.getSheetName())
            .headRowNumber(calculateHeadRowNumber(template))
            .doRead();

    return validationListener.getValidationResult();
}

// 3. Excel直接导入方法
private ValidationResult performExcelDirectImport(String filePath, GisManageTemplate template) {
    log.info("Excel直接导入模式 - 跳过验证，直接导入");

    // 检查前置条件
    if (!isValidationPassed(filePath)) {
        throw new IllegalStateException("Excel数据未通过验证，无法执行导入操作");
    }

    // 创建导入专用的ExcelDataListener
    ExcelDataListener importListener = new ExcelDataListener(template, "import", false); // validationOnly=false
    importListener.setSkipValidation(true); // 跳过验证步骤

    // 直接导入数据
    EasyExcel.read(filePath, importListener)
            .sheet(template.getSheetName())
            .headRowNumber(calculateHeadRowNumber(template))
            .doRead();

    return importListener.getImportResult();
}

// 4. 修改ExcelDataListener，支持验证模式标识
public class ExcelDataListener extends AnalysisEventListener<Map<Integer, Object>> {

    private boolean validationOnly = false;  // 是否只验证
    private boolean skipValidation = false;  // 是否跳过验证

    public ExcelDataListener(GisManageTemplate template, String target, boolean validationOnly) {
        this.template = template;
        this.target = target;
        this.validationOnly = validationOnly;
    }

    @Override
    public void invoke(Map<Integer, Object> data, AnalysisContext context) {
        if (validationOnly) {
            // 验证模式：只验证，不插入数据库
            performValidationOnly(data, context);
        } else {
            // 导入模式：根据skipValidation决定是否跳过验证
            if (skipValidation) {
                performDirectImport(data, context);
            } else {
                performValidationAndImport(data, context); // 原有逻辑
            }
        }
    }

    private void performValidationOnly(Map<Integer, Object> data, AnalysisContext context) {
        // 只执行验证逻辑，不调用数据库插入
        GeoFeatureEntity entity = convertToGeoFeatureEntity(data, context);
        ValidationResult.ValidationError error = validateEntity(entity);
        if (error != null) {
            errors.add(error);
        }
        validRecordCount++;
    }

    private void performDirectImport(Map<Integer, Object> data, AnalysisContext context) {
        // 跳过验证，直接转换和插入
        GeoFeatureEntity entity = convertToGeoFeatureEntityDirectly(data, context);
        batch.add(entity);

        if (batch.size() >= batchSize) {
            processBatchDirectly(); // 直接插入，不验证
        }
    }
}
```

## 阶段4：任务状态管理（0.5天）

### 任务4.1：实现任务状态管理
**优先级**：🔴 高
**目标**：支持验证-导入两阶段的状态跟踪

#### 修改文件：
1. `GisImportTaskServiceImpl.java`
2. `entity/GisImportTask.java`

#### 具体任务：
```java
// 1. 添加任务状态管理方法
@Service
public class TaskStatusManager {

    public void updateTaskValidationStatus(String filePath, String validationStatus, ValidationResult result) {
        GisImportTask task = findTaskByFilePath(filePath);
        if (task != null) {
            task.setValidationStatus(validationStatus);
            task.setValidationResult(JSON.toJSONString(result));
            task.setValidationTime(new Date());

            if ("VALIDATED".equals(validationStatus)) {
                task.setStatus("VALIDATION_COMPLETED");
                log.info("任务验证完成 - 任务ID: {}, 文件: {}", task.getId(), filePath);
            } else if ("VALIDATION_FAILED".equals(validationStatus)) {
                task.setStatus("VALIDATION_FAILED");
                log.warn("任务验证失败 - 任务ID: {}, 文件: {}", task.getId(), filePath);
            }

            gisImportTaskService.updateById(task);
        }
    }

    public void updateTaskImportStatus(String filePath, String importStatus, ValidationResult result) {
        GisImportTask task = findTaskByFilePath(filePath);
        if (task != null) {
            task.setImportStatus(importStatus);
            task.setImportResult(JSON.toJSONString(result));
            task.setImportTime(new Date());

            if ("IMPORTED".equals(importStatus)) {
                task.setStatus("IMPORT_COMPLETED");
                log.info("任务导入完成 - 任务ID: {}, 文件: {}", task.getId(), filePath);
            } else if ("IMPORT_FAILED".equals(importStatus)) {
                task.setStatus("IMPORT_FAILED");
                log.warn("任务导入失败 - 任务ID: {}, 文件: {}", task.getId(), filePath);
            }

            gisImportTaskService.updateById(task);
        }
    }

    public boolean isValidationPassed(String filePath) {
        GisImportTask task = findTaskByFilePath(filePath);
        return task != null && "VALIDATED".equals(task.getValidationStatus());
    }
}
```

## 阶段5：性能优化验证（0.5天）

### 任务5.1：性能测试和优化验证
**优先级**：🟡 中
**目标**：验证性能提升效果

#### 测试内容：
1. **验证模式性能测试**：
   - 测试纯验证模式的执行时间
   - 确认不执行数据库操作
   - 验证内存使用情况

2. **导入模式性能测试**：
   - 测试直接导入模式的执行时间
   - 对比原有验证+导入模式的性能
   - 目标：性能提升30-50%

3. **功能完整性测试**：
   - 验证两阶段流程的正确性
   - 确认任务状态转换正确
   - 验证错误处理机制

## 阶段6：后续优化任务（按需执行）

### 任务6.1：异步验证任务队列（可选）
**优先级**：🟢 低
**目标**：支持大文件的异步验证处理

### 任务6.2：验证规则扩展（可选）
**优先级**：🟢 低
**目标**：支持更多自定义验证规则

## 阶段6：前端UI控制逻辑（0.5天）

### 任务6.1：前端按钮状态控制
**优先级**：🟡 中
**目标**：根据data_status控制前端按钮的显示和可用性

#### 前端控制逻辑：
```javascript
// 根据data_status控制按钮状态
function updateButtonStates(dataStatus) {
    const validateBtn = document.getElementById('validateBtn');
    const importBtn = document.getElementById('importBtn');
    const statusText = document.getElementById('statusText');

    switch(dataStatus) {
        case 1: // 数据未检查
            validateBtn.disabled = false;
            validateBtn.textContent = '验证';
            importBtn.disabled = true;
            importBtn.textContent = '导入（需先验证）';
            statusText.textContent = '数据未检查 - 请先执行验证';
            statusText.className = 'status-warning';
            break;

        case 0: // 数据未导入（验证完成）
            validateBtn.disabled = false;
            validateBtn.textContent = '重新验证';
            importBtn.disabled = false;
            importBtn.textContent = '导入';
            statusText.textContent = '验证完成 - 可以执行导入';
            statusText.className = 'status-success';
            break;

        case 2: // 数据已导入
            validateBtn.disabled = false;
            validateBtn.textContent = '重新验证';
            importBtn.disabled = false;
            importBtn.textContent = '重新导入';
            statusText.textContent = '导入完成';
            statusText.className = 'status-completed';
            break;

        default:
            validateBtn.disabled = true;
            importBtn.disabled = true;
            statusText.textContent = '未知状态';
            statusText.className = 'status-error';
    }
}

// 验证按钮点击事件
function onValidateClick(taskId) {
    // 发送验证请求
    fetch(`/api/gis-import/validate/${taskId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: 'valid' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 验证成功，刷新页面状态
            location.reload();
        } else {
            alert('验证失败: ' + data.message);
        }
    });
}

// 导入按钮点击事件
function onImportClick(taskId) {
    // 发送导入请求
    fetch(`/api/gis-import/import/${taskId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: 'import' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 导入成功，刷新页面状态
            location.reload();
        } else {
            alert('导入失败: ' + data.message);
        }
    });
}
```

## 执行计划

### 第1天：状态管理和验证模式（1天）
- **上午（4小时）**：
  - 实现DataStatusManager服务
  - 添加状态审计字段
  - 实现PureValidationService
- **下午（4小时）**：
  - 修改TemplateBasedShapefileServiceImpl验证逻辑
  - 实现CoordinateValidationUtil
  - 单元测试验证模式

### 第2天：导入模式和前端控制（1天）
- **上午（4小时）**：
  - 实现DirectImportService
  - 修改ShapefileReaderServiceImpl支持直接导入
  - 修改Excel相关服务
- **下午（4小时）**：
  - 实现前端UI控制逻辑
  - 修改Controller层API
  - 集成测试

### 第3天：性能测试和优化（1天）
- **上午（4小时）**：
  - 性能基准测试
  - 验证模式性能测试
  - 导入模式性能测试
- **下午（4小时）**：
  - 问题修复和优化
  - 文档更新
  - 最终验收测试

## 验收标准

### 核心功能验收
1. **验证模式 (target="valid")**：
   - ✅ 只执行验证逻辑，不插入数据库
   - ✅ 验证字段映射、数据类型、坐标格式、业务规则
   - ✅ 返回详细的验证结果和错误信息
   - ✅ 更新任务状态为"验证完成"或"验证失败"

2. **导入模式 (target="import")**：
   - ✅ 检查前置条件：数据必须已验证通过
   - ✅ 跳过重复验证，直接执行数据库插入
   - ✅ 专注于数据转换、坐标系转换、批量插入
   - ✅ 更新任务状态为"导入完成"或"导入失败"

### 性能验收
1. **验证模式性能**：
   - ✅ 不执行数据库插入操作
   - ✅ 坐标只验证格式，不进行实际转换
   - ✅ 内存使用优化，适合大文件验证

2. **导入模式性能**：
   - ✅ 跳过验证步骤，性能提升30-50%
   - ✅ 直接执行数据转换和插入
   - ✅ 批量插入效率优化

### 业务流程验收
1. **两阶段流程**：
   - ✅ 验证阶段 → 导入阶段的正确流转
   - ✅ 任务状态正确更新和跟踪
   - ✅ 错误处理和异常恢复机制

2. **数据一致性**：
   - ✅ 验证结果与导入结果一致
   - ✅ 任务状态与实际执行状态一致
   - ✅ 错误信息准确和详细

### 测试用例
1. **验证模式测试**：
   ```
   输入：target="valid", 有效Shapefile文件
   期望：只验证不导入，任务状态更新为"验证完成"
   ```

2. **导入模式测试**：
   ```
   前置条件：数据已验证通过
   输入：target="import", 相同文件
   期望：跳过验证，直接导入，任务状态更新为"导入完成"
   ```

3. **错误处理测试**：
   ```
   输入：target="import", 未验证的文件
   期望：抛出异常，提示"数据未通过验证，无法执行导入操作"
   ```

### 性能基准
- **验证模式**：比原有模式快50%以上（因为不执行数据库操作）
- **导入模式**：比原有模式快30-50%（因为跳过验证步骤）
- **内存使用**：验证模式内存使用减少30%以上
- **响应时间**：验证模式响应时间减少60%以上

## 关键技术要点

### 1. 状态管理核心原则
- **单一职责**：每个状态只负责一个明确的业务阶段
- **状态不可逆**：data_status只能按 1→0→2 的顺序转换
- **操作幂等**：重复执行相同操作不会产生副作用
- **审计完整**：所有状态变更都有详细的日志记录

### 2. 验证模式优化策略
- **跳过数据库操作**：只进行内存中的数据验证
- **轻量级坐标检查**：只验证格式和范围，不进行坐标系转换
- **批量验证**：一次性读取所有数据进行验证
- **错误快速失败**：遇到严重错误立即停止验证

### 3. 导入模式优化策略
- **跳过重复验证**：信任验证阶段的结果
- **直接数据转换**：专注于数据转换和插入
- **批量插入优化**：使用高性能批量插入
- **事务管理**：确保数据一致性

### 4. 前端交互优化
- **状态驱动UI**：根据data_status动态控制按钮状态
- **实时反馈**：提供清晰的状态提示和操作指导
- **错误处理**：友好的错误提示和恢复建议
- **进度显示**：长时间操作提供进度反馈

## 风险控制和应急预案

### 主要风险识别
1. **数据一致性风险**：验证和导入阶段的数据可能不一致
2. **状态管理风险**：任务状态更新失败导致流程混乱
3. **性能回归风险**：优化后可能引入新的性能问题
4. **业务流程风险**：用户操作习惯改变可能导致混乱

### 风险缓解措施
1. **数据一致性保障**：
   - 文件MD5校验确保文件未变更
   - 验证结果包含文件元信息和时间戳
   - 导入前再次检查文件完整性和验证状态
   - 数据库事务确保状态更新的原子性

2. **状态管理保障**：
   - 数据库事务确保状态更新的原子性
   - 状态更新失败的自动重试机制
   - 详细的状态变更审计日志
   - 状态异常的自动检测和告警

3. **性能监控保障**：
   - 实时性能监控和告警
   - 性能回归自动化测试
   - 性能基准对比和趋势分析
   - 性能问题的快速定位和修复

4. **业务流程保障**：
   - 详细的用户操作指南和培训
   - 渐进式功能发布和用户反馈
   - 操作错误的友好提示和恢复指导
   - 紧急情况下的快速回滚机制

### 应急预案
1. **数据不一致应急**：
   - 立即停止相关操作
   - 检查文件和数据库状态
   - 重新执行验证流程
   - 必要时回滚到上一个稳定状态

2. **状态管理异常应急**：
   - 手动修复任务状态
   - 重新同步状态和实际数据
   - 检查状态变更日志
   - 修复状态管理逻辑

3. **性能问题应急**：
   - 监控系统资源使用情况
   - 临时降级到原有处理模式
   - 分析性能瓶颈和优化方案
   - 逐步恢复优化功能

## 成功标准

### 技术指标
- ✅ 验证模式性能提升50%以上
- ✅ 导入模式性能提升30%以上
- ✅ 内存使用优化30%以上
- ✅ 响应时间减少60%以上
- ✅ 错误率降低到1%以下

### 业务指标
- ✅ 用户操作流程更加清晰
- ✅ 任务状态管理更加精确
- ✅ 错误处理更加友好
- ✅ 系统稳定性显著提升

### 质量指标
- ✅ 代码覆盖率达到90%以上
- ✅ 集成测试通过率100%
- ✅ 用户验收测试通过
- ✅ 性能测试达标

这个重构方案基于现有的data_status字段，严格按照"验证-导入"两阶段业务流程设计，确保验证和导入职责分离，避免重复工作，显著提升系统性能和用户体验，同时保持系统的稳定性和可维护性。
