# Excel线表坐标转换功能说明

## 功能概述

在Excel数据处理流程中，新增了对线表（type=3）的坐标转换支持。当模板配置为Excel类型且type=3时，系统会自动处理线要素的起点和终点坐标，并支持坐标系转换。

## 配置要求

### 1. 模板配置条件
- `template_type` = "excel"
- `type` = 3（线表）
- `is_zh` = true（需要坐标转换时）

### 2. 线坐标映射配置（line_map字段）
```json
{
  "x": 5,                    // 起点X坐标所在Excel列号
  "y": 6,                    // 起点Y坐标所在Excel列号
  "x1": 9,                   // 终点X坐标所在Excel列号
  "y1": 10,                  // 终点Y坐标所在Excel列号
  "qdbh": "",                // 起点编号字段（可选）
  "zdbh": "",                // 终点编号字段（可选）
  "targetPointTable": "",    // 目标点表（可选）
  "pointTableBh": ""         // 点表编号字段（可选）
}
```

### 3. 坐标系配置
- `original_coordinate_system`: 源坐标系（如"WenZhou2000"）
- `target_coordinate_system`: 目标坐标系（如"CGCS2000"）

## 处理流程

### 阶段1：数据读取和解析
1. **Excel行数据读取**：EasyExcel逐行读取Excel数据
2. **坐标字段提取**：根据line_map配置提取x、y、x1、y1列的坐标值
3. **坐标验证**：验证坐标值格式和范围
4. **几何生成**：构建LINESTRING WKT几何数据

```java
// 示例：Excel第5、6、9、10列包含坐标数据
Map<Integer, Object> rowData = {
    5: "459048.432",    // 起点X
    6: "3056094.039",   // 起点Y
    9: "459039.772",    // 终点X
    10: "3056064.731"   // 终点Y
}

// 生成的WKT几何
String wkt = "LINESTRING(459048.432 3056094.039, 459039.772 3056064.731)";
```

### 阶段2：批次坐标转换
1. **条件检查**：验证是否需要坐标转换（is_zh=true）
2. **几何转换**：调用坐标转换服务转换LINESTRING几何
3. **坐标解析**：从转换后的WKT中解析新的坐标值
4. **属性更新**：更新实体的坐标属性和几何数据

```java
// 转换前
LINESTRING(459048.432 3056094.039, 459039.772 3056064.731)

// 转换后（示例：WenZhou2000 -> CGCS2000）
LINESTRING(120.123456 30.234567, 120.123789 30.234890)
```

### 阶段3：数据库插入
1. **字段映射**：将转换后的坐标映射到数据库字段
2. **批量插入**：使用HighPerformanceBatchInsertService插入数据

## 数据结构

### GeoFeatureEntity属性
```java
// 坐标属性
entity.setAttribute("x_coord", 120.123456);    // 转换后的起点X
entity.setAttribute("y_coord", 30.234567);     // 转换后的起点Y
entity.setAttribute("x1_coord", 120.123789);   // 转换后的终点X
entity.setAttribute("y1_coord", 30.234890);    // 转换后的终点Y

// 几何属性
entity.setGeometry("LINESTRING(120.123456 30.234567, 120.123789 30.234890)");
```

## 与点表对比

| 特性 | 点表 (type=2) | 线表 (type=3) |
|------|---------------|---------------|
| 配置字段 | point_map | line_map |
| 坐标数量 | 2个 (x, y) | 4个 (x, y, x1, y1) |
| 几何类型 | POINT | LINESTRING |
| 存储属性 | x_coord, y_coord | x_coord, y_coord, x1_coord, y1_coord |

## 错误处理

### 1. 配置错误
- 缺少line_map配置：跳过坐标处理
- 坐标列配置不完整：记录警告并跳过
- 模板类型不匹配：跳过坐标处理

### 2. 数据错误
- 坐标值为空：记录调试信息并跳过
- 坐标格式错误：记录警告并跳过该行
- 坐标范围超限：抛出异常

### 3. 转换错误
- 坐标转换失败：记录错误但不中断处理
- WKT解析失败：记录警告并保持原坐标

## 日志示例

```
2025-07-30 09:13:23.953 INFO  - 开始线坐标转换: (459048.432, 3056094.039) - (459039.772, 3056064.731) from WenZhou2000 to CGCS2000
2025-07-30 09:13:23.954 INFO  - 线坐标转换完成: (120.123456, 30.234567) - (120.123789, 30.234890)
2025-07-30 09:13:23.955 DEBUG - 线坐标字段更新完成: x=120.123456, y=30.234567, x1=120.123789, y1=30.234890
```

## 使用示例

### 1. 模板配置
```sql
UPDATE gis_manage_template SET 
  template_type = 'excel',
  type = 3,
  is_zh = true,
  original_coordinate_system = 'WenZhou2000',
  target_coordinate_system = 'CGCS2000',
  line_map = '{"x": 5, "y": 6, "x1": 9, "y1": 10, "qdbh": "", "zdbh": "", "targetPointTable": "", "pointTableBh": ""}'
WHERE id = 1826;
```

### 2. Excel数据格式
| 列0 | 列1 | 列2 | 列3 | 列4 | 列5(起点X) | 列6(起点Y) | 列7 | 列8 | 列9(终点X) | 列10(终点Y) |
|-----|-----|-----|-----|-----|------------|------------|-----|-----|------------|-------------|
| 管道001 | PE | 200 | ... | ... | 459048.432 | 3056094.039 | ... | ... | 459039.772 | 3056064.731 |

### 3. 处理结果
- 原始坐标：WenZhou2000投影坐标
- 转换坐标：CGCS2000地理坐标
- 几何数据：LINESTRING WKT格式
- 数据库存储：转换后的坐标值

## 扩展性

该功能设计具有良好的扩展性：

1. **支持更多几何类型**：可扩展支持多线段、面要素等
2. **支持更多坐标系**：通过CoordinateTransformService支持
3. **支持复杂线要素**：可扩展支持多点线要素
4. **支持字段映射**：可将转换后坐标映射到特定数据库字段

## 注意事项

1. **性能考虑**：坐标转换在批次处理阶段进行，避免频繁的服务调用
2. **数据一致性**：转换失败时保持原始数据，确保数据不丢失
3. **配置验证**：系统会验证模板配置的完整性和正确性
4. **向后兼容**：不影响现有的点表和纯文本模板处理逻辑
