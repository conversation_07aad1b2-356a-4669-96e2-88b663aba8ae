# Excel坐标映射强制列名格式说明

## 修改概述

已将Excel导入系统的坐标映射配置修改为强制使用列名格式，移除了所有向后兼容性代码，确保配置的统一性和简洁性。

## 强制配置格式

### 唯一支持的配置格式

#### 线表坐标映射（line_map）
```json
{
    "x": "起点横坐标",
    "y": "起点纵坐标", 
    "x1": "终点横坐标",
    "y1": "终点纵坐标",
    "qdbh": "",
    "zdbh": "",
    "targetPointTable": "",
    "pointTableBh": ""
}
```

#### 点表坐标映射（point_map）
```json
{
    "x": "X坐标",
    "y": "Y坐标"
}
```

### 配置要求

1. **必须是字符串类型**：所有坐标字段（x、y、x1、y1）的值必须是字符串
2. **不能为空**：不允许null值或空字符串
3. **必须是有效列名**：字符串必须对应Excel表头中的实际列名

## 不再支持的格式

### ❌ 整数位置配置（已移除）
```json
{
    "x": 5,     // 不再支持
    "y": 6,     // 不再支持
    "x1": 9,    // 不再支持
    "y1": 10    // 不再支持
}
```

### ❌ 扩展格式配置（已移除）
```json
{
    "x": 5,
    "xColumnName": "起点横坐标",  // 不再支持
    "y": 6,
    "yColumnName": "起点纵坐标"   // 不再支持
}
```

### ❌ 空值配置（已移除）
```json
{
    "x": "",     // 不再支持
    "y": null,   // 不再支持
    "x1": "终点横坐标",
    "y1": "终点纵坐标"
}
```

## 错误处理和验证

### 1. 配置类型错误
```java
// 错误配置
"x": 5

// 错误信息
"坐标字段 'x' 的配置值必须是字符串类型的列名，当前类型: Integer，当前值: 5。
正确的配置格式示例: \"x\": \"起点横坐标\""
```

### 2. 空值配置错误
```java
// 错误配置
"y": null

// 错误信息
"坐标字段 'y' 的配置值不能为空。请使用列名字符串配置，例如: \"y\": \"起点横坐标\""
```

### 3. 空字符串配置错误
```java
// 错误配置
"x1": ""

// 错误信息
"坐标字段 'x1' 的列名不能为空字符串。请配置有效的Excel列名，例如: \"x1\": \"起点横坐标\""
```

### 4. 列名不存在错误
```java
// 错误配置（列名在Excel中不存在）
"y1": "不存在的列名"

// 错误信息
"Excel表头中未找到坐标字段 'y1' 配置的列名 '不存在的列名'。
请检查Excel表头是否包含该列名，或修改配置中的列名。"
```

## 核心实现

### 简化的getCoordinateColumnIndex方法
```java
private Integer getCoordinateColumnIndex(Map<String, Object> coordinateMap, String coordinateKey) {
    // 验证参数
    if (coordinateMap == null || coordinateKey == null) {
        throw new RuntimeException("坐标映射配置或坐标键名不能为空");
    }

    Object coordinateValue = coordinateMap.get(coordinateKey);
    
    // 强制验证：不能为null
    if (coordinateValue == null) {
        throw new RuntimeException(String.format(
            "坐标字段 '%s' 的配置值不能为空。请使用列名字符串配置", coordinateKey));
    }

    // 强制验证：必须是字符串类型
    if (!(coordinateValue instanceof String)) {
        throw new RuntimeException(String.format(
            "坐标字段 '%s' 的配置值必须是字符串类型的列名", coordinateKey));
    }

    String columnName = ((String) coordinateValue).trim();
    
    // 强制验证：不能是空字符串
    if (columnName.isEmpty()) {
        throw new RuntimeException(String.format(
            "坐标字段 '%s' 的列名不能为空字符串", coordinateKey));
    }

    // 查找列索引
    Integer columnIndex = getColumnIndexByName(columnName);
    if (columnIndex == null) {
        throw new RuntimeException(String.format(
            "Excel表头中未找到坐标字段 '%s' 配置的列名 '%s'", coordinateKey, columnName));
    }

    return columnIndex;
}
```

### 强化的验证逻辑
```java
private void validateCoordinateMapColumnNames(Map<String, Object> coordinateMap, 
                                            List<String> missingColumns, 
                                            String mapType, 
                                            String[] coordinateKeys) {
    if (coordinateMap == null) {
        throw new RuntimeException(String.format("%s坐标映射配置不能为空", mapType));
    }
    
    for (String key : coordinateKeys) {
        Object value = coordinateMap.get(key);
        
        // 强制验证配置格式
        if (value == null) {
            throw new RuntimeException(String.format(
                "%s坐标字段 '%s' 的配置值不能为空", mapType, key));
        }
        
        if (!(value instanceof String)) {
            throw new RuntimeException(String.format(
                "%s坐标字段 '%s' 的配置值必须是字符串类型的列名", mapType, key));
        }
        
        String columnName = ((String) value).trim();
        if (columnName.isEmpty()) {
            throw new RuntimeException(String.format(
                "%s坐标字段 '%s' 的列名不能为空字符串", mapType, key));
        }
        
        // 检查列名是否存在
        if (!headerMapping.containsKey(columnName)) {
            missingColumns.add(columnName);
        }
    }
}
```

## 使用示例

### 正确的配置示例

#### Excel文件结构
```
| 管道编号 | 材质 | 起点横坐标  | 起点纵坐标   | 终点横坐标  | 终点纵坐标   |
|----------|------|-------------|-------------|-------------|-------------|
| PIPE001  | PE   | 492670.037  | 3107642.153 | 492670.411  | 3107640.474 |
```

#### 模板配置
```json
{
    "id": 877,
    "templateType": "excel",
    "type": 3,
    "sheetName": "管线数据",
    "lineMap": {
        "x": "起点横坐标",
        "y": "起点纵坐标",
        "x1": "终点横坐标",
        "y1": "终点纵坐标",
        "qdbh": "",
        "zdbh": "",
        "targetPointTable": "",
        "pointTableBh": ""
    }
}
```

### 错误的配置示例

#### ❌ 使用整数位置
```json
{
    "lineMap": {
        "x": 2,  // 错误：不再支持整数位置
        "y": "起点纵坐标",
        "x1": "终点横坐标",
        "y1": "终点纵坐标"
    }
}
```

#### ❌ 使用空值
```json
{
    "lineMap": {
        "x": "",  // 错误：不允许空字符串
        "y": null,  // 错误：不允许null值
        "x1": "终点横坐标",
        "y1": "终点纵坐标"
    }
}
```

## 迁移指南

### 从旧格式迁移

#### 步骤1：识别当前配置
```sql
-- 查找使用整数位置配置的模板
SELECT id, template_name, line_map, point_map
FROM gis_manage_template 
WHERE template_type = 'excel' 
  AND (
    JSON_TYPE(JSON_EXTRACT(line_map, '$.x')) = 'INTEGER' OR
    JSON_TYPE(JSON_EXTRACT(point_map, '$.x')) = 'INTEGER'
  );
```

#### 步骤2：更新配置
```sql
-- 更新线表配置
UPDATE gis_manage_template 
SET line_map = JSON_SET(
    line_map,
    '$.x', '起点横坐标',
    '$.y', '起点纵坐标',
    '$.x1', '终点横坐标',
    '$.y1', '终点纵坐标'
)
WHERE id = 877;

-- 更新点表配置
UPDATE gis_manage_template 
SET point_map = JSON_SET(
    point_map,
    '$.x', 'X坐标',
    '$.y', 'Y坐标'
)
WHERE id = 876;
```

#### 步骤3：验证配置
```sql
-- 验证更新后的配置
SELECT 
    id,
    template_name,
    CASE 
        WHEN type = 2 THEN point_map
        WHEN type = 3 THEN line_map
    END as coordinate_config
FROM gis_manage_template 
WHERE template_type = 'excel' 
  AND (type = 2 OR type = 3);
```

## 测试验证

### 单元测试覆盖
- ✅ 正确的列名配置测试
- ✅ 整数配置抛出异常测试
- ✅ 空字符串配置抛出异常测试
- ✅ null配置抛出异常测试
- ✅ 缺失列名的错误处理测试

### 测试用例示例
```java
@Test
void testInvalidIntegerConfigurationThrowsException() {
    Map<String, Object> lineMap = new HashMap<>();
    lineMap.put("x", 0);  // 整数配置（不再支持）
    
    RuntimeException exception = assertThrows(RuntimeException.class, () -> {
        getCoordinateColumnIndex(lineMap, "x");
    });
    
    assertTrue(exception.getMessage().contains("配置值必须是字符串类型的列名"));
}
```

## 优势和效果

### 1. 配置统一性
- 所有坐标配置都使用相同的列名格式
- 消除了多种配置方式带来的混乱
- 提高了配置的可读性和可维护性

### 2. 代码简洁性
- 移除了复杂的兼容性逻辑
- 减少了代码分支和判断
- 提高了代码的可读性和性能

### 3. 错误提示清晰
- 明确指出配置错误的具体原因
- 提供正确的配置格式示例
- 帮助用户快速定位和修复问题

### 4. 维护成本降低
- 减少了需要维护的代码路径
- 降低了测试复杂度
- 简化了文档和培训材料

## 注意事项

### 1. 破坏性变更
- 这是一个破坏性变更，不兼容旧的配置格式
- 需要更新所有现有的模板配置
- 建议在测试环境充分验证后再部署到生产环境

### 2. 数据备份
- 更新配置前务必备份数据库
- 保留原始配置以便必要时回滚
- 准备回滚方案和脚本

### 3. 用户培训
- 更新相关文档和操作手册
- 培训用户使用新的配置格式
- 提供配置迁移的技术支持

这个修改使Excel坐标映射配置更加统一和简洁，虽然移除了向后兼容性，但提供了更清晰的配置体验和更好的代码维护性。
