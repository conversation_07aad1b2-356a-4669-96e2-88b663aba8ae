# Excel坐标映射错误修复说明

## 问题描述

系统在处理Excel线坐标时抛出RuntimeException，错误信息显示无法在Excel表头中找到列名'起点横坐标'。

**错误详情**：
- 错误发生在第161行处理Excel数据第2行时
- 堆栈跟踪显示错误源自`getCoordinateColumnIndex()`方法第1095行
- 系统查找列名'起点横坐标'但在Excel表头映射中找不到

## 根本原因分析

### 错误的架构逻辑

**问题根源**：代码错误地从`line_map`配置中查找坐标列名，但实际上这些列名定义在`map`字段映射中。

**错误逻辑流程**：
```
processLineCoordinates() 
  -> getCoordinateColumnIndex(lineMap, "x") 
  -> 在lineMap中查找"x"对应的列名
  -> 但lineMap中配置的是字段引用，不是实际的Excel列名
```

**正确逻辑应该是**：
```
processLineCoordinates() 
  -> getCoordinateColumnIndexFromFieldMapping("qdhzb") 
  -> 在字段映射中查找fieldName="qdhzb"的配置
  -> 获取该配置的columnName="起点横坐标"
  -> 使用columnName在Excel表头中查找列索引
```

### 配置结构分析

**字段映射配置（map）**：
```json
[
  {
    "columnName": "起点横坐标",
    "checked": true,
    "position": 5,
    "fieldName": "qdhzb",
    "fieldType": "double precision"
  },
  {
    "columnName": "起点纵坐标", 
    "checked": true,
    "position": 6,
    "fieldName": "qdzzb",
    "fieldType": "double precision"
  },
  {
    "columnName": "终点横坐标",
    "checked": true, 
    "position": 9,
    "fieldName": "zdhzb",
    "fieldType": "double precision"
  },
  {
    "columnName": "终点纵坐标",
    "checked": true,
    "position": 10, 
    "fieldName": "zdzzb",
    "fieldType": "double precision"
  }
]
```

**线坐标映射配置（line_map）**：
```json
{
  "x": "起点横坐标",    // 错误：这里应该引用字段名，不是列名
  "y": "起点纵坐标",    // 错误：这里应该引用字段名，不是列名
  "x1": "终点横坐标",   // 错误：这里应该引用字段名，不是列名
  "y1": "终点纵坐标"    // 错误：这里应该引用字段名，不是列名
}
```

## 修复方案

### 1. 架构调整

**新的数据映射流程**：
1. **主要数据映射**：所有Excel数据字段（包括坐标字段）都通过`map`配置进行映射
2. **坐标转换参考**：`line_map`配置仅在坐标转换操作时使用，用于标识哪些字段包含需要转换的坐标数据

### 2. 核心修改

#### 2.1 新增字段映射查找方法
```java
/**
 * 从字段映射中获取坐标字段的列索引
 * @param fieldName 数据库字段名（如：qdhzb, qdzzb, zdhzb, zdzzb）
 * @return 列索引，如果找不到则返回null
 */
private Integer getCoordinateColumnIndexFromFieldMapping(String fieldName) {
    List<Map<String, Object>> fieldMappings = template.getMap();
    if (fieldMappings == null) {
        return null;
    }

    for (Map<String, Object> mapping : fieldMappings) {
        String mappingFieldName = (String) mapping.get("fieldName");
        Boolean checked = (Boolean) mapping.get("checked");
        String columnName = (String) mapping.get("columnName");

        // 找到匹配的字段名且已启用
        if (fieldName.equals(mappingFieldName) && (checked == null || checked)) {
            if (columnName != null && !columnName.trim().isEmpty()) {
                Integer columnIndex = getColumnIndexByName(columnName);
                if (columnIndex != null) {
                    log.debug("坐标字段 {} 从字段映射获取列索引: '{}' -> {}", 
                             fieldName, columnName, columnIndex);
                    return columnIndex;
                } else {
                    log.warn("坐标字段 {} 的列名 '{}' 在Excel表头中未找到", 
                            fieldName, columnName);
                    return null;
                }
            }
        }
    }

    log.debug("未在字段映射中找到坐标字段: {}", fieldName);
    return null;
}
```

#### 2.2 修改线坐标处理逻辑
```java
private void processLineCoordinates(Map<Integer, Object> rowData, Map<String, Object> attributes) {
    log.debug("开始处理线坐标数据");

    // 从字段映射中获取坐标字段的列索引
    Integer xColumn = getCoordinateColumnIndexFromFieldMapping("qdhzb");   // 起点横坐标
    Integer yColumn = getCoordinateColumnIndexFromFieldMapping("qdzzb");   // 起点纵坐标
    Integer x1Column = getCoordinateColumnIndexFromFieldMapping("zdhzb");  // 终点横坐标
    Integer y1Column = getCoordinateColumnIndexFromFieldMapping("zdzzb");  // 终点纵坐标

    if (xColumn == null || yColumn == null || x1Column == null || y1Column == null) {
        log.warn("线坐标字段映射不完整");
        log.warn("请检查字段映射(map)中是否包含以下坐标字段:");
        log.warn("  - qdhzb (起点横坐标)");
        log.warn("  - qdzzb (起点纵坐标)");
        log.warn("  - zdhzb (终点横坐标)");
        log.warn("  - zdzzb (终点纵坐标)");
        
        debugFieldMappings();
        return;
    }

    // 继续处理坐标数据...
}
```

#### 2.3 增强调试功能
```java
/**
 * 输出字段映射的调试信息
 */
private void debugFieldMappings() {
    List<Map<String, Object>> fieldMappings = template.getMap();
    if (fieldMappings == null || fieldMappings.isEmpty()) {
        log.warn("字段映射配置为空");
        return;
    }

    log.info("=== 字段映射调试信息 ===");
    log.info("共有 {} 个字段映射", fieldMappings.size());
    
    for (int i = 0; i < fieldMappings.size(); i++) {
        Map<String, Object> mapping = fieldMappings.get(i);
        String fieldName = (String) mapping.get("fieldName");
        String columnName = (String) mapping.get("columnName");
        Integer position = (Integer) mapping.get("position");
        Boolean checked = (Boolean) mapping.get("checked");
        
        log.info("字段映射[{}]: fieldName='{}', columnName='{}', position={}, checked={}", 
                i, fieldName, columnName, position, checked);
        
        // 检查是否是坐标字段
        if (fieldName != null && (fieldName.equals("qdhzb") || fieldName.equals("qdzzb") || 
                                 fieldName.equals("zdhzb") || fieldName.equals("zdzzb"))) {
            log.info("  -> 这是坐标字段！");
            if (columnName != null) {
                Integer columnIndex = getColumnIndexByName(columnName);
                log.info("  -> 列名 '{}' 对应的列索引: {}", columnName, columnIndex);
            }
        }
    }
    log.info("=== 字段映射调试信息结束 ===");
}
```

#### 2.4 修改验证逻辑
```java
/**
 * 验证坐标映射中的列名
 * 注意：坐标字段现在通过字段映射(map)进行处理，不再强制验证line_map/point_map中的配置
 */
private void validateCoordinateMappingColumnNames(List<String> missingColumns) {
    // 对于线表，检查字段映射中是否包含坐标字段
    if (template.getType() != null && template.getType() == 3) {
        validateLineCoordinateFieldMappings(missingColumns);
    }

    // 对于点表，检查字段映射中是否包含坐标字段
    if (template.getType() != null && template.getType() == 2) {
        validatePointCoordinateFieldMappings(missingColumns);
    }

    // 可选：验证line_map/point_map配置（仅用于坐标转换时的参考）
    validateOptionalCoordinateMaps(missingColumns);
}
```

## 修复效果

### 1. 解决的问题
- ✅ 修复了"找不到列名'起点横坐标'"的错误
- ✅ 统一了数据映射逻辑，所有字段都通过`map`配置处理
- ✅ 明确了`line_map`的作用范围（仅用于坐标转换参考）
- ✅ 增强了调试能力，便于问题排查

### 2. 架构改进
- **数据映射统一性**：所有Excel字段都通过字段映射处理
- **职责分离**：数据映射与坐标转换逻辑分离
- **配置清晰性**：每个配置的作用范围更加明确

### 3. 向后兼容性
- 保持现有的字段映射配置格式不变
- `line_map`配置仍然保留，用于坐标转换时的参考
- 不影响其他类型的数据导入

## 使用说明

### 1. 字段映射配置要求
确保字段映射中包含坐标字段：
```json
{
  "fieldName": "qdhzb",        // 数据库字段名
  "columnName": "起点横坐标",   // Excel列名
  "checked": true,             // 启用该字段
  "fieldType": "double precision"
}
```

### 2. 坐标字段命名规范
- **线表坐标字段**：
  - `qdhzb` - 起点横坐标
  - `qdzzb` - 起点纵坐标
  - `zdhzb` - 终点横坐标
  - `zdzzb` - 终点纵坐标

- **点表坐标字段**：
  - `x_coord` 或 `x` - X坐标
  - `y_coord` 或 `y` - Y坐标

### 3. 调试建议
如果遇到坐标映射问题：
1. 检查日志中的"字段映射调试信息"
2. 确认坐标字段的`fieldName`和`columnName`配置正确
3. 验证Excel表头中确实包含配置的列名
4. 确保坐标字段的`checked`属性为true

这个修复确保了Excel坐标映射逻辑的正确性和一致性，解决了列名查找错误的问题。
