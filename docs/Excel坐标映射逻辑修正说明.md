# Excel坐标映射逻辑修正说明

## 问题澄清

经过进一步分析，我之前对坐标映射逻辑的理解有误。正确的逻辑应该是：

### 正确的坐标映射架构

1. **点表坐标获取**：通过`point_map`字段配置获取坐标数据
   ```json
   {
     "x": "X坐标",      // x对应的Excel列名
     "y": "Y坐标"       // y对应的Excel列名
   }
   ```

2. **线表坐标获取**：通过`line_map`字段配置获取坐标数据
   ```json
   {
     "x": "起点横坐标",   // x对应的Excel列名
     "y": "起点纵坐标",   // y对应的Excel列名
     "x1": "终点横坐标",  // x1对应的Excel列名
     "y1": "终点纵坐标"   // y1对应的Excel列名
   }
   ```

3. **字段映射（map）**：用于普通数据字段的映射，不直接用于坐标处理

## 修正的实现

### 1. 线坐标处理逻辑

```java
private void processLineCoordinates(Map<Integer, Object> rowData, Map<String, Object> attributes) {
    log.debug("开始处理线坐标数据");

    Map<String, Object> lineMap = template.getLineMap();
    if (lineMap == null) {
        log.debug("线坐标映射配置为空，跳过处理");
        return;
    }

    // 从line_map配置中获取坐标字段的列索引
    // line_map格式: {"x":"起点横坐标","y":"起点纵坐标","x1":"终点横坐标","y1":"终点纵坐标"}
    Integer xColumn = getCoordinateColumnIndex(lineMap, "x");    // 起点X
    Integer yColumn = getCoordinateColumnIndex(lineMap, "y");    // 起点Y
    Integer x1Column = getCoordinateColumnIndex(lineMap, "x1");  // 终点X
    Integer y1Column = getCoordinateColumnIndex(lineMap, "y1");  // 终点Y

    if (xColumn == null || yColumn == null || x1Column == null || y1Column == null) {
        log.warn("线坐标映射配置不完整: 起点X列索引={}, 起点Y列索引={}, 终点X列索引={}, 终点Y列索引={}",
                xColumn, yColumn, x1Column, y1Column);
        log.warn("请检查line_map配置中的坐标字段映射:");
        log.warn("  当前line_map配置: {}", lineMap);
        log.warn("  期望格式: {{\"x\":\"起点横坐标\",\"y\":\"起点纵坐标\",\"x1\":\"终点横坐标\",\"y1\":\"终点纵坐标\"}}");
        log.warn("  当前Excel表头映射: {}", headerMapping.keySet());
        return;
    }

    // 继续处理坐标数据...
}
```

### 2. 点坐标处理逻辑

```java
private void processPointCoordinates(Map<Integer, Object> rowData, Map<String, Object> attributes) {
    log.debug("开始处理点坐标数据");

    Map<String, Object> pointMap = template.getPointMap();
    if (pointMap == null) {
        log.debug("点坐标映射配置为空，跳过处理");
        return;
    }

    // 从point_map配置中获取坐标字段的列索引
    // point_map格式: {"x":"X坐标","y":"Y坐标"}
    Integer xColumn = getCoordinateColumnIndex(pointMap, "x");
    Integer yColumn = getCoordinateColumnIndex(pointMap, "y");

    if (xColumn == null || yColumn == null) {
        log.warn("点坐标映射配置不完整: x列索引={}, y列索引={}", xColumn, yColumn);
        log.warn("请检查point_map配置中的坐标字段映射:");
        log.warn("  当前point_map配置: {}", pointMap);
        log.warn("  期望格式: {{\"x\":\"X坐标\",\"y\":\"Y坐标\"}}");
        log.warn("  当前Excel表头映射: {}", headerMapping.keySet());
        return;
    }

    // 继续处理坐标数据...
}
```

### 3. 坐标验证逻辑

```java
private void validateCoordinateMappingColumnNames(List<String> missingColumns) {
    // 验证点坐标映射
    if (template.getType() != null && template.getType() == 2) {
        validateCoordinateMapColumnNames(template.getPointMap(), missingColumns, "点坐标", 
            new String[]{"x", "y"});
    }
    
    // 验证线坐标映射
    if (template.getType() != null && template.getType() == 3) {
        validateCoordinateMapColumnNames(template.getLineMap(), missingColumns, "线坐标", 
            new String[]{"x", "y", "x1", "y1"});
    }
}
```

## 配置示例

### 线表配置示例

```json
{
  "id": 877,
  "templateType": "excel",
  "type": 3,
  "sheetName": "管线数据",
  "map": [
    {
      "columnName": "管道编号",
      "fieldName": "pipe_code",
      "checked": true,
      "fieldType": "string"
    }
  ],
  "lineMap": {
    "x": "起点横坐标",
    "y": "起点纵坐标",
    "x1": "终点横坐标",
    "y1": "终点纵坐标",
    "qdbh": "",
    "zdbh": "",
    "targetPointTable": "",
    "pointTableBh": ""
  }
}
```

### 点表配置示例

```json
{
  "id": 876,
  "templateType": "excel",
  "type": 2,
  "sheetName": "设备点位",
  "map": [
    {
      "columnName": "设备编号",
      "fieldName": "device_code",
      "checked": true,
      "fieldType": "string"
    }
  ],
  "pointMap": {
    "x": "经度",
    "y": "纬度"
  }
}
```

## 数据处理流程

### 1. Excel表头示例
```
| 管道编号 | 材质 | 起点横坐标  | 起点纵坐标   | 终点横坐标  | 终点纵坐标   |
|----------|------|-------------|-------------|-------------|-------------|
| PIPE001  | PE   | 492670.037  | 3107642.153 | 492670.411  | 3107640.474 |
```

### 2. 处理流程
```
1. 读取表头：建立列名到列索引的映射
   {"起点横坐标" -> 2, "起点纵坐标" -> 3, "终点横坐标" -> 4, "终点纵坐标" -> 5}

2. 处理普通字段：通过map配置映射
   "管道编号" -> pipe_code

3. 处理坐标字段：通过line_map配置映射
   line_map["x"] = "起点横坐标" -> 查找列索引2 -> 获取值492670.037
   line_map["y"] = "起点纵坐标" -> 查找列索引3 -> 获取值3107642.153
   line_map["x1"] = "终点横坐标" -> 查找列索引4 -> 获取值492670.411
   line_map["y1"] = "终点纵坐标" -> 查找列索引5 -> 获取值3107640.474

4. 生成几何数据：
   LINESTRING(492670.037 3107642.153, 492670.411 3107640.474)
```

## 关键修正点

### 1. 坐标字段来源
- **修正前**：错误地从字段映射（map）中查找坐标字段
- **修正后**：正确地从point_map/line_map中查找坐标字段

### 2. 配置格式理解
- **修正前**：认为line_map中存储的是字段引用
- **修正后**：理解line_map中存储的是实际的Excel列名

### 3. 验证逻辑
- **修正前**：验证字段映射中的坐标字段
- **修正后**：验证point_map/line_map中配置的列名是否在Excel表头中存在

## 错误排查

### 1. 常见错误
```
错误：Excel表头中未找到坐标字段 'x' 配置的列名 '起点横坐标'
原因：Excel表头中没有名为"起点横坐标"的列
解决：检查Excel文件表头或修改line_map配置
```

### 2. 调试信息
系统会输出以下调试信息：
```
当前line_map配置: {"x":"起点横坐标","y":"起点纵坐标","x1":"终点横坐标","y1":"终点纵坐标"}
期望格式: {"x":"起点横坐标","y":"起点纵坐标","x1":"终点横坐标","y1":"终点纵坐标"}
当前Excel表头映射: [管道编号, 材质, 起点横坐标, 起点纵坐标, 终点横坐标, 终点纵坐标]
```

### 3. 配置检查清单
- ✅ line_map/point_map配置不为空
- ✅ 配置中的列名与Excel表头完全匹配（包括空格、标点符号）
- ✅ Excel表头读取正确（检查数据开始行配置）
- ✅ 坐标字段的checked属性为true（如果在字段映射中也配置了）

这个修正确保了坐标映射逻辑的正确性，符合系统的实际设计架构。
