# Excel坐标映射支持列名配置说明

## 修改概述

已成功扩展Excel导入系统的坐标映射功能，支持直接使用列名进行坐标字段配置，使配置更加直观和易于维护。

## 新的配置格式

### 1. 线表坐标映射（line_map）

#### 新的推荐格式（直接使用列名）
```json
{
    "x": "起点横坐标",
    "y": "起点纵坐标", 
    "x1": "终点横坐标",
    "y1": "终点纵坐标",
    "qdbh": "",
    "zdbh": "",
    "targetPointTable": "",
    "pointTableBh": ""
}
```

#### 向后兼容格式（使用列索引）
```json
{
    "x": 5,
    "y": 6,
    "x1": 9,
    "y1": 10,
    "qdbh": "",
    "zdbh": "",
    "targetPointTable": "",
    "pointTableBh": ""
}
```

#### 扩展格式（兼容旧的扩展配置）
```json
{
    "x": 5,
    "y": 6,
    "x1": 9,
    "y1": 10,
    "xColumnName": "起点横坐标",
    "yColumnName": "起点纵坐标",
    "x1ColumnName": "终点横坐标",
    "y1ColumnName": "终点纵坐标",
    "qdbh": "",
    "zdbh": "",
    "targetPointTable": "",
    "pointTableBh": ""
}
```

### 2. 点表坐标映射（point_map）

#### 新的推荐格式
```json
{
    "x": "X坐标",
    "y": "Y坐标"
}
```

#### 向后兼容格式
```json
{
    "x": 3,
    "y": 4
}
```

## 配置优先级

系统按以下优先级处理坐标字段配置：

1. **直接列名配置**（最高优先级）
   - 如果坐标键（x、y、x1、y1）的值是字符串，则作为列名处理
   - 示例：`"x": "起点横坐标"`

2. **扩展列名配置**（中等优先级）
   - 使用 `坐标键 + "ColumnName"` 格式的配置
   - 示例：`"xColumnName": "起点横坐标"`

3. **位置索引配置**（最低优先级，向后兼容）
   - 如果坐标键的值是整数，则作为列索引处理
   - 示例：`"x": 5`

## 实现细节

### 1. 核心方法修改

#### getCoordinateColumnIndex方法
```java
private Integer getCoordinateColumnIndex(Map<String, Object> coordinateMap, String coordinateKey) {
    Object coordinateValue = coordinateMap.get(coordinateKey);
    
    // 方式1：直接配置列名（推荐）
    if (coordinateValue instanceof String) {
        String columnName = ((String) coordinateValue).trim();
        if (!columnName.isEmpty()) {
            return getColumnIndexByName(columnName);
        }
    }
    
    // 方式2：配置列索引（向后兼容）
    if (coordinateValue instanceof Integer) {
        return (Integer) coordinateValue;
    }
    
    // 方式3：扩展列名配置（兼容性）
    String columnNameKey = coordinateKey + "ColumnName";
    String columnName = (String) coordinateMap.get(columnNameKey);
    if (columnName != null && !columnName.trim().isEmpty()) {
        return getColumnIndexByName(columnName);
    }
    
    return null;
}
```

### 2. 验证增强

#### 坐标列名验证
```java
private void validateCoordinateMappingColumnNames(List<String> missingColumns) {
    // 验证点坐标映射
    if (template.getType() == 2) {
        validateCoordinateMapColumnNames(template.getPointMap(), missingColumns, "点坐标", 
            new String[]{"x", "y"});
    }
    
    // 验证线坐标映射
    if (template.getType() == 3) {
        validateCoordinateMapColumnNames(template.getLineMap(), missingColumns, "线坐标", 
            new String[]{"x", "y", "x1", "y1"});
    }
}
```

## 使用示例

### 1. Excel文件结构示例

```
| 管道编号 | 材质 | 起点横坐标  | 起点纵坐标   | 终点横坐标  | 终点纵坐标   |
|----------|------|-------------|-------------|-------------|-------------|
| PIPE001  | PE   | 492670.037  | 3107642.153 | 492670.411  | 3107640.474 |
| PIPE002  | PVC  | 492671.123  | 3107643.456 | 492671.789  | 3107641.234 |
```

### 2. 完整的模板配置示例

```json
{
    "id": 877,
    "templateType": "excel",
    "type": 3,
    "sheetName": "管线数据",
    "map": [
        {
            "checked": true,
            "columnName": "管道编号",
            "fieldName": "pipe_code",
            "fieldType": "string"
        },
        {
            "checked": true,
            "columnName": "材质",
            "fieldName": "material",
            "fieldType": "string"
        }
    ],
    "lineMap": {
        "x": "起点横坐标",
        "y": "起点纵坐标",
        "x1": "终点横坐标",
        "y1": "终点纵坐标",
        "qdbh": "",
        "zdbh": "",
        "targetPointTable": "",
        "pointTableBh": ""
    },
    "isZh": true,
    "originalCoordinateSystem": "WenZhou2000",
    "targetCoordinateSystem": "CGCS2000"
}
```

### 3. 数据处理流程

```java
1. 读取Excel表头：
   {"管道编号", "材质", "起点横坐标", "起点纵坐标", "终点横坐标", "终点纵坐标"}

2. 建立列名映射：
   {"起点横坐标" -> 2, "起点纵坐标" -> 3, "终点横坐标" -> 4, "终点纵坐标" -> 5}

3. 验证坐标列名：
   检查line_map中配置的所有列名是否在表头中存在

4. 处理数据行：
   - 根据"起点横坐标"找到列索引2，获取值492670.037
   - 根据"起点纵坐标"找到列索引3，获取值3107642.153
   - 根据"终点横坐标"找到列索引4，获取值492670.411
   - 根据"终点纵坐标"找到列索引5，获取值3107640.474

5. 生成几何数据：
   LINESTRING(492670.037 3107642.153, 492670.411 3107640.474)

6. 坐标转换（如果需要）：
   WenZhou2000 -> CGCS2000

7. 数据库插入：
   使用转换后的坐标值
```

## 错误处理

### 1. 列名不存在错误
```
错误信息：Excel表中未找到以下列名: '起点横坐标', '终点纵坐标'，请检查表头是否正确
```

### 2. 配置格式错误
```java
// 空字符串配置
"x": ""  // 返回null，跳过该坐标字段

// null值配置
"y": null  // 返回null，跳过该坐标字段

// 无效类型配置
"x1": []  // 返回null，跳过该坐标字段
```

### 3. 日志信息
```
DEBUG - 坐标字段 x 使用直接列名映射: '起点横坐标' -> 列索引 2
DEBUG - 坐标字段 y 使用位置映射: 列索引 3
WARN  - 坐标字段 x1 的列名 '终点横坐标' 在Excel表头中未找到
```

## 迁移指南

### 1. 从位置配置迁移到列名配置

#### 迁移前（位置配置）
```json
{
    "x": 5,
    "y": 6,
    "x1": 9,
    "y1": 10
}
```

#### 迁移后（列名配置）
```json
{
    "x": "起点横坐标",
    "y": "起点纵坐标",
    "x1": "终点横坐标",
    "y1": "终点纵坐标"
}
```

### 2. 数据库更新脚本示例

```sql
-- 更新现有模板的line_map配置
UPDATE gis_manage_template 
SET line_map = JSON_SET(
    line_map,
    '$.x', '起点横坐标',
    '$.y', '起点纵坐标',
    '$.x1', '终点横坐标',
    '$.y1', '终点纵坐标'
)
WHERE id = 877 AND template_type = 'excel' AND type = 3;

-- 更新point_map配置
UPDATE gis_manage_template 
SET point_map = JSON_SET(
    point_map,
    '$.x', 'X坐标',
    '$.y', 'Y坐标'
)
WHERE template_type = 'excel' AND type = 2;
```

## 优势和效果

### 1. 配置直观性
- **修改前**：`"x": 5` - 需要记住第5列是什么
- **修改后**：`"x": "起点横坐标"` - 直接看到列的含义

### 2. 维护便利性
- Excel列顺序变化时，只要列名不变，配置仍然有效
- 配置更有语义，便于理解和维护
- 减少因列位置变化导致的配置错误

### 3. 错误提示改善
- 明确指出缺失的列名
- 区分字段映射错误和坐标映射错误
- 提供详细的调试日志

### 4. 向后兼容性
- 完全兼容现有的位置配置
- 支持混合使用不同的配置方式
- 平滑的迁移路径

## 测试覆盖

### 单元测试验证
- ✅ 直接列名配置的坐标映射
- ✅ 缺失坐标列的错误处理
- ✅ 坐标列索引获取逻辑
- ✅ 向后兼容性（整数位置配置）
- ✅ 空配置和无效配置处理

### 集成测试建议
1. **完整流程测试**：使用新配置格式测试完整的Excel导入流程
2. **坐标转换测试**：验证列名配置下的坐标转换功能
3. **混合配置测试**：测试同时使用列名和位置配置的情况
4. **错误恢复测试**：测试各种错误情况下的系统行为

这个改进使Excel坐标映射配置更加直观和易于维护，同时保持了完全的向后兼容性，为用户提供了更好的配置体验。
