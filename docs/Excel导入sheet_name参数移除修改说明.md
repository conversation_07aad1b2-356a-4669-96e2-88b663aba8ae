# Excel导入sheet_name参数移除修改说明

## 修改概述

根据要求，已成功移除Excel导入和验证逻辑中的sheet_name参数传递，改为使用模板配置中的sheet_name字段。

## 修改内容

### 1. ExcelImportService接口修改

#### 文件：`src/main/java/com/zjxy/gisimportservice/service/ExcelImportService.java`

**修改前**：
```java
ExcelImportResult importExcelData(
    MultipartFile file, 
    Integer templateId, 
    String sheetName,  // ❌ 移除此参数
    String target,
    String createdBy
) throws IOException, ParseException;

Map<String, Object> validateExcelData(
    MultipartFile file,
    Integer templateId,
    String sheetName   // ❌ 移除此参数
) throws IOException;

ValidationResult validateExcelData(String filePath, GisManageTemplate template,
                                 String sheetName, String target, String createdBy);  // ❌ 移除sheetName参数
```

**修改后**：
```java
ExcelImportResult importExcelData(
    MultipartFile file, 
    Integer templateId, 
    String target,
    String createdBy
) throws IOException, ParseException;

Map<String, Object> validateExcelData(
    MultipartFile file,
    Integer templateId
) throws IOException;

ValidationResult validateExcelData(String filePath, GisManageTemplate template,
                                 String target, String createdBy);
```

### 2. ExcelImportServiceImpl实现类修改

#### 文件：`src/main/java/com/zjxy/gisimportservice/service/Impl/ExcelImportServiceImpl.java`

#### 2.1 importExcelData方法
**修改前**：
```java
public ExcelImportResult importExcelData(MultipartFile file, Integer templateId,
                                       String sheetName, String target, String createdBy)
```

**修改后**：
```java
public ExcelImportResult importExcelData(MultipartFile file, Integer templateId,
                                       String target, String createdBy)
```

#### 2.2 executeExcelImport方法
**修改前**：
```java
private ExcelImportResult executeExcelImport(MultipartFile file, GisManageTemplate template,
                                           String sheetName, String target, GisImportTask task)
```

**修改后**：
```java
private ExcelImportResult executeExcelImport(MultipartFile file, GisManageTemplate template,
                                           String target, GisImportTask task)
```

#### 2.3 工作表名称获取逻辑
**修改前**：
```java
// 确定工作表名称
String targetSheetName = sheetName;
if (targetSheetName == null || targetSheetName.trim().isEmpty()) {
    targetSheetName = template.getSheetName();
    if (targetSheetName == null || targetSheetName.trim().isEmpty()) {
        targetSheetName = null; // 读取第一个工作表
    }
}
```

**修改后**：
```java
// 从模板配置中获取工作表名称
String sheetName = template.getSheetName();
if (sheetName == null || sheetName.trim().isEmpty()) {
    throw new RuntimeException("模板配置缺少工作表名称，请在模板中配置sheet_name字段");
}
```

#### 2.4 Excel读取逻辑
**修改前**：
```java
if (targetSheetName != null && !targetSheetName.trim().isEmpty()) {
    EasyExcel.read(inputStream, testListener)
            .sheet(targetSheetName)
            .headRowNumber(headRowNumber)
            .doRead();
} else {
    EasyExcel.read(inputStream, testListener)
            .sheet(0)  // 读取第一个工作表
            .headRowNumber(headRowNumber)
            .doRead();
}
```

**修改后**：
```java
try {
    log.info("开始读取Excel工作表: '{}'", sheetName);
    EasyExcel.read(inputStream, listener)
            .sheet(sheetName)
            .headRowNumber(headRowNumber)
            .doRead();
    log.info("Excel工作表读取完成: '{}'", sheetName);
} catch (Exception e) {
    String errorMessage = e.getMessage();
    if (errorMessage != null && (errorMessage.contains("sheet") || errorMessage.contains("工作表"))) {
        throw new RuntimeException("找不到工作表 '" + sheetName + "'，请检查Excel文件中是否存在该工作表");
    } else {
        throw new RuntimeException("读取Excel工作表 '" + sheetName + "' 时发生错误: " + errorMessage, e);
    }
}
```

#### 2.5 validateExcelData方法
**修改前**：
```java
public ValidationResult validateExcelData(String filePath, GisManageTemplate template,
                                        String sheetName, String target, String createdBy)
```

**修改后**：
```java
public ValidationResult validateExcelData(String filePath, GisManageTemplate template,
                                        String target, String createdBy)
```

### 3. GisImportTaskServiceImpl调用修改

#### 文件：`src/main/java/com/zjxy/gisimportservice/service/Impl/GisImportTaskServiceImpl.java`

**修改前**：
```java
return excelImportService.validateExcelData(task.getFilePath(), template, null, "valid", task.getCreatedBy());

ValidationResult importResult = excelImportService.validateExcelData(task.getFilePath(), template, null, "import", task.getCreatedBy());
```

**修改后**：
```java
return excelImportService.validateExcelData(task.getFilePath(), template, "valid", task.getCreatedBy());

ValidationResult importResult = excelImportService.validateExcelData(task.getFilePath(), template, "import", task.getCreatedBy());
```

## 新增功能

### 1. 错误处理机制

#### 模板配置验证
```java
String sheetName = template.getSheetName();
if (sheetName == null || sheetName.trim().isEmpty()) {
    throw new RuntimeException("模板配置缺少工作表名称，请在模板中配置sheet_name字段");
}
```

#### 工作表不存在处理
```java
try {
    EasyExcel.read(inputStream, listener).sheet(sheetName).doRead();
} catch (Exception e) {
    if (errorMessage.contains("sheet") || errorMessage.contains("工作表")) {
        throw new RuntimeException("找不到工作表 '" + sheetName + "'，请检查Excel文件中是否存在该工作表");
    } else {
        throw new RuntimeException("读取Excel工作表 '" + sheetName + "' 时发生错误: " + errorMessage, e);
    }
}
```

### 2. 日志增强
```java
log.info("使用模板配置的工作表名称: {}", sheetName);
log.info("开始读取Excel工作表: '{}'", sheetName);
log.info("Excel工作表读取完成: '{}'", sheetName);
```

## 模板配置要求

### GisManageTemplate实体
确保模板配置中包含`sheetName`字段：
```java
/**
 * Sheet名称（Excel导入时使用）
 */
private String sheetName;
```

### 数据库配置
需要在`gis_manage_template`表中为每个Excel模板配置正确的`sheet_name`值：
```sql
UPDATE gis_manage_template 
SET sheet_name = '工作表名称' 
WHERE id = 模板ID AND template_type = 'excel';
```

## 向后兼容性

### 保持的功能
- 所有现有的Excel导入功能保持不变
- Shapefile导入不受影响
- 模板管理功能不受影响

### 移除的功能
- 不再支持通过API参数指定工作表名称
- 不再支持默认读取第一个工作表（必须明确配置）

## 错误信息

### 配置错误
- **错误**：`"模板配置缺少工作表名称，请在模板中配置sheet_name字段"`
- **解决**：在模板配置中添加正确的`sheet_name`值

### 工作表不存在
- **错误**：`"找不到工作表 'SheetName'，请检查Excel文件中是否存在该工作表"`
- **解决**：确保Excel文件中存在指定名称的工作表，或修改模板配置中的`sheet_name`

### 读取错误
- **错误**：`"读取Excel工作表 'SheetName' 时发生错误: 具体错误信息"`
- **解决**：检查Excel文件格式和工作表内容

## 测试建议

### 1. 正常流程测试
- 配置正确的`sheet_name`，验证能正常读取指定工作表
- 测试不同名称的工作表

### 2. 错误处理测试
- 测试模板未配置`sheet_name`的情况
- 测试Excel文件中不存在指定工作表的情况
- 测试工作表名称包含特殊字符的情况

### 3. 兼容性测试
- 验证现有的Excel导入功能正常工作
- 验证Shapefile导入不受影响

## 部署注意事项

1. **数据库更新**：确保所有Excel模板都配置了正确的`sheet_name`
2. **前端调整**：如果前端有传递`sheetName`参数的代码，需要移除
3. **API文档更新**：更新相关API文档，移除`sheetName`参数说明
4. **监控日志**：部署后监控是否有模板配置缺失的错误
