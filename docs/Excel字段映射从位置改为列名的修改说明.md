# Excel字段映射从位置改为列名的修改说明

## 修改概述

已成功将Excel导入系统的字段映射逻辑从基于列位置（position）改为基于列名（columnName），同时保持向后兼容性。

## 修改内容

### 1. 核心修改：ExcelDataListener.java

#### 1.1 新增成员变量
```java
/**
 * 表头映射：列名 -> 列索引
 */
private Map<String, Integer> headerMapping = new HashMap<>();

/**
 * 是否已读取表头
 */
private boolean headerRead = false;
```

#### 1.2 修改invoke方法
**新增表头读取逻辑**：
```java
// 如果是第一行且还没有读取表头，则读取表头
if (!headerRead && isHeaderRow(context)) {
    readHeader(data);
    return;
}
```

#### 1.3 新增表头处理方法

##### readHeader方法
```java
private void readHeader(Map<Integer, Object> headerData) {
    // 建立列名到列索引的映射
    for (Map.Entry<Integer, Object> entry : headerData.entrySet()) {
        Integer columnIndex = entry.getKey();
        Object columnNameObj = entry.getValue();
        
        if (columnNameObj != null) {
            String columnName = columnNameObj.toString().trim();
            if (!columnName.isEmpty()) {
                headerMapping.put(columnName, columnIndex);
            }
        }
    }
    
    // 验证模板配置中的列名是否都存在
    validateColumnNames();
}
```

##### validateColumnNames方法
```java
private void validateColumnNames() {
    List<String> missingColumns = new ArrayList<>();
    
    for (Map<String, Object> mapping : fieldMappings) {
        String columnName = (String) mapping.get("columnName");
        if (columnName != null && !headerMapping.containsKey(columnName)) {
            missingColumns.add(columnName);
        }
    }
    
    if (!missingColumns.isEmpty()) {
        throw new RuntimeException("Excel表中未找到以下列名: " + 
            String.join(", ", missingColumns) + "，请检查表头是否正确");
    }
}
```

#### 1.4 修改字段映射逻辑

**修改前（基于位置）**：
```java
Integer position = (Integer) mapping.get("position");
Object value = rowData.get(position);
```

**修改后（基于列名，向后兼容）**：
```java
// 优先使用columnName，如果没有则回退到position
String columnName = (String) mapping.get("columnName");
Integer position = (Integer) mapping.get("position");

Integer columnIndex = null;
if (columnName != null && !columnName.trim().isEmpty()) {
    // 使用列名映射
    columnIndex = getColumnIndexByName(columnName);
} else if (position != null) {
    // 回退到位置映射（向后兼容）
    columnIndex = position;
}

Object value = rowData.get(columnIndex);
```

#### 1.5 修改坐标字段处理

##### 新增getCoordinateColumnIndex方法
```java
private Integer getCoordinateColumnIndex(Map<String, Object> coordinateMap, String coordinateKey) {
    // 优先尝试使用列名配置
    String columnNameKey = coordinateKey + "ColumnName";
    String columnName = (String) coordinateMap.get(columnNameKey);
    
    if (columnName != null && !columnName.trim().isEmpty()) {
        return getColumnIndexByName(columnName);
    }
    
    // 回退到位置配置（向后兼容）
    Object positionObj = coordinateMap.get(coordinateKey);
    if (positionObj instanceof Integer) {
        return (Integer) positionObj;
    }
    
    return null;
}
```

##### 修改坐标处理方法
```java
// 修改前
Integer xColumn = (Integer) pointMap.get("x");
Integer yColumn = (Integer) pointMap.get("y");

// 修改后
Integer xColumn = getCoordinateColumnIndex(pointMap, "x");
Integer yColumn = getCoordinateColumnIndex(pointMap, "y");
```

## 2. 配置格式说明

### 2.1 新的模板配置格式

#### 字段映射配置
```json
{
    "checked": true,
    "position": 0,                    // 向后兼容，可选
    "columnName": "管道编号",          // 新增：Excel列名
    "fieldName": "pipe_code",         // 数据库字段名
    "fieldType": "string"             // 字段类型
}
```

#### 坐标映射配置（点表）
```json
{
    "x": 5,                          // 向后兼容：位置
    "y": 6,                          // 向后兼容：位置
    "xColumnName": "X坐标",          // 新增：X坐标列名
    "yColumnName": "Y坐标"           // 新增：Y坐标列名
}
```

#### 坐标映射配置（线表）
```json
{
    "x": 5,                          // 向后兼容：起点X位置
    "y": 6,                          // 向后兼容：起点Y位置
    "x1": 9,                         // 向后兼容：终点X位置
    "y1": 10,                        // 向后兼容：终点Y位置
    "xColumnName": "起点X坐标",      // 新增：起点X列名
    "yColumnName": "起点Y坐标",      // 新增：起点Y列名
    "x1ColumnName": "终点X坐标",     // 新增：终点X列名
    "y1ColumnName": "终点Y坐标"      // 新增：终点Y列名
}
```

### 2.2 映射优先级

1. **字段映射优先级**：
   - 如果存在`columnName`且不为空 → 使用列名映射
   - 否则如果存在`position` → 使用位置映射（向后兼容）
   - 否则跳过该字段

2. **坐标映射优先级**：
   - 如果存在`xColumnName`等列名配置 → 使用列名映射
   - 否则如果存在`x`等位置配置 → 使用位置映射（向后兼容）
   - 否则跳过坐标处理

## 3. 错误处理增强

### 3.1 列名验证错误
```java
// 错误信息示例
"Excel表中未找到以下列名: '管道编号', '材质'，请检查表头是否正确"
```

### 3.2 表头读取错误
```java
// 在数据处理开始前进行验证，避免处理过程中出错
// 如果表头验证失败，立即抛出异常，不会进行数据处理
```

### 3.3 日志增强
```java
// 详细的映射日志
log.info("映射字段成功: {} (列名'{}') = {} (原始值: {})", fieldName, columnName, convertedValue, value);
log.debug("坐标字段 {} 使用列名映射: '{}' -> 列索引 {}", coordinateKey, columnName, columnIndex);
```

## 4. 向后兼容性

### 4.1 完全兼容现有配置
- 现有基于`position`的配置无需修改，继续正常工作
- 新的`columnName`配置可以逐步添加

### 4.2 混合使用支持
- 同一个模板中可以混合使用`position`和`columnName`
- 每个字段独立选择映射方式

### 4.3 迁移策略
```sql
-- 为现有模板添加columnName配置
UPDATE gis_manage_template 
SET map = JSON_SET(map, '$[0].columnName', '管道编号')
WHERE id = 模板ID;
```

## 5. 使用示例

### 5.1 Excel文件结构
```
| 序号 | 管道编号 | 材质 | 管径 | X坐标      | Y坐标       |
|------|----------|------|------|------------|-------------|
| 1    | PIPE001  | PE   | 200  | 120.123456 | 30.234567   |
| 2    | PIPE002  | PVC  | 300  | 120.123789 | 30.234890   |
```

### 5.2 新的模板配置
```json
{
    "map": [
        {
            "checked": true,
            "columnName": "管道编号",
            "fieldName": "pipe_code",
            "fieldType": "string"
        },
        {
            "checked": true,
            "columnName": "材质",
            "fieldName": "material",
            "fieldType": "string"
        },
        {
            "checked": true,
            "columnName": "管径",
            "fieldName": "diameter",
            "fieldType": "integer"
        }
    ],
    "pointMap": {
        "xColumnName": "X坐标",
        "yColumnName": "Y坐标"
    }
}
```

### 5.3 处理流程
```java
1. 读取表头行：{"序号", "管道编号", "材质", "管径", "X坐标", "Y坐标"}
2. 建立映射：{"管道编号" -> 1, "材质" -> 2, "管径" -> 3, "X坐标" -> 4, "Y坐标" -> 5}
3. 验证列名：检查配置中的所有columnName是否都存在
4. 处理数据行：根据列名找到对应的列索引，获取数据
5. 字段映射：将Excel数据映射到数据库字段
```

## 6. 测试验证

### 6.1 单元测试覆盖
- ✅ 表头读取和映射建立
- ✅ 基于列名的字段映射
- ✅ 缺失列名的错误处理
- ✅ 向后兼容性（仅position配置）
- ✅ 列名优先级（columnName优先于position）

### 6.2 集成测试建议
1. **正常流程测试**：使用包含列名的Excel文件测试完整导入流程
2. **错误处理测试**：测试表头缺失列名的情况
3. **兼容性测试**：使用旧的position配置测试向后兼容性
4. **混合配置测试**：测试同时使用position和columnName的情况

## 7. 优势和效果

### 7.1 用户体验改善
- **直观易懂**：用户可以通过列名进行映射，无需记住列位置
- **灵活性强**：Excel列顺序变化时，只要列名不变，映射仍然有效
- **错误提示清晰**：明确指出缺失的列名，便于用户快速定位问题

### 7.2 系统可维护性
- **配置更清晰**：列名比位置更有语义，便于理解和维护
- **向后兼容**：不影响现有系统，平滑升级
- **扩展性好**：为未来的功能扩展提供了更好的基础

### 7.3 数据处理可靠性
- **早期验证**：在数据处理前验证列名，避免处理过程中出错
- **详细日志**：提供详细的映射日志，便于问题排查
- **容错机制**：支持部分列名缺失的情况，提高系统健壮性

这个修改显著提升了Excel导入系统的易用性和可维护性，同时保持了完全的向后兼容性，为用户提供了更好的使用体验。
