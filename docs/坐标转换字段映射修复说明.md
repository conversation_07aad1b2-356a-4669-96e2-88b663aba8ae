# 坐标转换字段映射修复说明

## 问题描述

在Excel线表坐标转换功能中发现一个关键问题：虽然坐标转换成功，但数据库插入时仍使用原始坐标而不是转换后的坐标。

### 问题现象

**转换成功的日志**：
```
几何字段 geom: LINESTRING(120.592092900762 28.08298566333718, 120.59209812305714 28.082966797883092)
```

**但数据库插入使用的是原始坐标**：
```
数据字段 qdzzb (源字段: col_6): 3107642.153 -> 3107642.153  // 原始Y坐标
数据字段 qdhzb (源字段: col_5): 492670.037 -> 492670.037    // 原始X坐标
```

## 根本原因

### 1. 字段映射机制
Excel数据处理使用双重字段映射：
- **数据库字段名**：如 `qdzzb`, `qdhzb` 等
- **列位置字段**：如 `col_5`, `col_6` 等

### 2. 插入逻辑
`HighPerformanceBatchInsertService` 在构建SQL参数时，使用的是**列位置字段**（`col_x`）的值，而不是转换后的坐标字段（`x_coord`, `y_coord`）。

### 3. 转换后更新不完整
坐标转换后只更新了：
- `x_coord`, `y_coord`, `x1_coord`, `y1_coord`（坐标字段）
- `geometry`（几何字段）

但没有更新：
- `col_5`, `col_6`, `col_9`, `col_10`（原始列位置字段）

## 修复方案

### 1. 新增字段更新方法

#### 线表坐标更新
```java
/**
 * 更新转换后的线坐标到原始字段映射
 */
private void updateTransformedLineCoordinates(GeoFeatureEntity entity, double x, double y, double x1, double y1) {
    Map<String, Object> lineMap = template.getLineMap();
    if (lineMap == null) return;

    // 更新原始字段，确保数据库插入时使用转换后的坐标
    Integer xColumn = (Integer) lineMap.get("x");
    if (xColumn != null) {
        entity.setAttribute("col_" + xColumn, x);
    }
    
    Integer yColumn = (Integer) lineMap.get("y");
    if (yColumn != null) {
        entity.setAttribute("col_" + yColumn, y);
    }
    
    // 同样处理 x1, y1...
}
```

#### 点表坐标更新
```java
/**
 * 更新转换后的点坐标到原始字段映射
 */
private void updateTransformedPointCoordinates(GeoFeatureEntity entity, double x, double y) {
    Map<String, Object> pointMap = template.getPointMap();
    if (pointMap == null) return;

    // 更新原始字段
    Integer xColumn = (Integer) pointMap.get("x");
    if (xColumn != null) {
        entity.setAttribute("col_" + xColumn, x);
    }
    
    Integer yColumn = (Integer) pointMap.get("y");
    if (yColumn != null) {
        entity.setAttribute("col_" + yColumn, y);
    }
}
```

### 2. 修改转换方法调用

在坐标转换完成后，调用字段更新方法：

```java
// 线表转换
if (transformedCoords != null && transformedCoords.length >= 2) {
    double newX = transformedCoords[0][0];
    double newY = transformedCoords[0][1];
    double newX1 = transformedCoords[1][0];
    double newY1 = transformedCoords[1][1];

    // 更新坐标属性
    entity.setAttribute("x_coord", newX);
    entity.setAttribute("y_coord", newY);
    entity.setAttribute("x1_coord", newX1);
    entity.setAttribute("y1_coord", newY1);
    
    // 🔥 关键修复：更新原始字段映射
    updateTransformedLineCoordinates(entity, newX, newY, newX1, newY1);
    
    // 更新几何
    entity.setGeometry(transformedWkt);
}
```

## 修复效果

### 修复前
```java
// 转换后的坐标
entity.getAttribute("x_coord") = 120.592092900762
entity.getAttribute("y_coord") = 28.08298566333718

// 但原始字段仍是旧值
entity.getAttribute("col_5") = 492670.037  // ❌ 原始坐标
entity.getAttribute("col_6") = 3107642.153 // ❌ 原始坐标

// 数据库插入使用 col_5, col_6 的值 -> 插入原始坐标
```

### 修复后
```java
// 转换后的坐标
entity.getAttribute("x_coord") = 120.592092900762
entity.getAttribute("y_coord") = 28.08298566333718

// 原始字段也被更新
entity.getAttribute("col_5") = 120.592092900762  // ✅ 转换后坐标
entity.getAttribute("col_6") = 28.08298566333718 // ✅ 转换后坐标

// 数据库插入使用 col_5, col_6 的值 -> 插入转换后坐标
```

## 验证方法

### 1. 日志验证
查看转换后的日志，应该显示：
```
更新转换后起点X字段 col_5: 120.592092900762
更新转换后起点Y字段 col_6: 28.08298566333718
更新转换后终点X字段 col_9: 120.59209812305714
更新转换后终点Y字段 col_10: 28.082966797883092
```

### 2. 数据库验证
检查数据库中插入的坐标值：
```sql
SELECT qdzzb, qdhzb, zdzzb, zdhzb, ST_AsText(geom) as geometry
FROM target_table 
WHERE id = 'test_record';
```

应该显示转换后的地理坐标（如120.xx, 28.xx）而不是原始投影坐标（如492xxx, 310xxx）。

### 3. 单元测试
运行 `ExcelLineCoordinateTest.testLineCoordinateTransformation()` 测试，验证：
- 坐标字段正确更新
- 原始字段正确更新
- 几何数据正确更新

## 影响范围

### 受影响的功能
- Excel线表（type=3）坐标转换
- Excel点表（type=2）坐标转换
- 所有需要坐标系转换的Excel导入任务

### 不受影响的功能
- Shapefile导入（使用不同的转换逻辑）
- 纯文本Excel导入（type=1，无坐标处理）
- 不需要坐标转换的Excel导入（is_zh=false）

## 注意事项

1. **向后兼容**：修复不影响现有的不需要坐标转换的功能
2. **性能影响**：字段更新操作很轻量，对性能影响微乎其微
3. **数据一致性**：确保坐标字段和原始字段保持一致
4. **错误处理**：转换失败时保持原始数据不变

## 部署建议

1. **测试环境验证**：先在测试环境验证修复效果
2. **备份数据**：部署前备份相关数据表
3. **监控日志**：部署后监控坐标转换相关日志
4. **数据验证**：抽样检查转换后的数据正确性
