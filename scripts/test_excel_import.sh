#!/bin/bash

# Excel导入测试脚本
# 用于验证修复后的Excel导入功能

echo "=== Excel导入功能测试 ==="

# 配置
API_BASE_URL="http://localhost:8080/api"
TEMPLATE_ID=874
SHEET_NAME="燃气管线"

echo "1. 检查Excel导入配置..."
curl -s "${API_BASE_URL}/excel-import/config" | jq '.'

echo -e "\n2. 检查模板信息..."
curl -s "${API_BASE_URL}/template/${TEMPLATE_ID}" | jq '.data | {id, nameZh, thLine, sheetName, templateType, excelDataStartRow}'

echo -e "\n3. 测试Excel文件分析..."
# 注意：需要替换为实际的Excel文件路径
EXCEL_FILE="path/to/your/excel/file.xlsx"

if [ -f "$EXCEL_FILE" ]; then
    echo "分析Excel文件: $EXCEL_FILE"
    curl -X POST \
        -F "file=@${EXCEL_FILE}" \
        -F "headerRow=1" \
        "${API_BASE_URL}/excel-import/analyze" | jq '.'
else
    echo "Excel文件不存在: $EXCEL_FILE"
    echo "请将实际的Excel文件路径替换到脚本中"
fi

echo -e "\n4. 测试Excel导入..."
if [ -f "$EXCEL_FILE" ]; then
    echo "导入Excel文件: $EXCEL_FILE"
    curl -X POST \
        -F "file=@${EXCEL_FILE}" \
        -F "templateId=${TEMPLATE_ID}" \
        -F "sheetName=${SHEET_NAME}" \
        -F "target=import" \
        "${API_BASE_URL}/excel-import/import" | jq '.'
else
    echo "跳过导入测试，Excel文件不存在"
fi

echo -e "\n=== 测试完成 ==="
